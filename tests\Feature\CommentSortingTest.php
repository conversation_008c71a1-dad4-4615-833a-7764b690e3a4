<?php

use App\Models\User;
use App\Models\Post;
use App\Models\Comment;
use App\Models\Like;

test('comments can be fetched with different sorting options', function () {
    // Create a user and post
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);
    
    // Create comments with different timestamps and likes
    $baseTime = now();

    $comment1 = Comment::create([
        'content' => 'First comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    $comment1->created_at = $baseTime->copy()->subHours(3);
    $comment1->updated_at = $baseTime->copy()->subHours(3);
    $comment1->save();

    $comment2 = Comment::create([
        'content' => 'Second comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    $comment2->created_at = $baseTime->copy()->subHours(2);
    $comment2->updated_at = $baseTime->copy()->subHours(2);
    $comment2->save();

    $comment3 = Comment::create([
        'content' => 'Third comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    $comment3->created_at = $baseTime->copy()->subHours(1);
    $comment3->updated_at = $baseTime->copy()->subHours(1);
    $comment3->save();
    
    // Add likes to make comment1 most relevant
    Like::create(['user_id' => $user->id, 'likeable_type' => Comment::class, 'likeable_id' => $comment1->id]);
    Like::create(['user_id' => User::factory()->create()->id, 'likeable_type' => Comment::class, 'likeable_id' => $comment1->id]);
    
    // Test newest sorting
    $response = $this->actingAs($user)->getJson("/posts/{$post->id}/comments?sort=newest");
    $response->assertStatus(200);
    $response->assertJson(['success' => true]);

    $comments = $response->json('comments');
    expect($comments[0]['id'])->toBe($comment3->id); // Most recent first
    expect($comments[1]['id'])->toBe($comment2->id);
    expect($comments[2]['id'])->toBe($comment1->id);

    // Test oldest sorting
    $response = $this->actingAs($user)->getJson("/posts/{$post->id}/comments?sort=oldest");
    $response->assertStatus(200);

    $comments = $response->json('comments');
    expect($comments[0]['id'])->toBe($comment1->id); // Oldest first
    expect($comments[1]['id'])->toBe($comment2->id);
    expect($comments[2]['id'])->toBe($comment3->id);
    
    // Test most relevant sorting (should prioritize likes)
    $response = $this->actingAs($user)->getJson("/posts/{$post->id}/comments?sort=most_relevant");
    $response->assertStatus(200);
    
    $comments = $response->json('comments');
    expect($comments[0]['id'])->toBe($comment1->id); // Most liked first
    expect($comments[0]['likes_count'])->toBe(2);
    expect($comments[0]['is_liked_by_user'])->toBe(true);
});

test('comment sorting includes replies with proper structure', function () {
    $user = User::factory()->create();
    $post = Post::factory()->create(['user_id' => $user->id]);
    
    // Create parent comment
    $parentComment = Comment::create([
        'content' => 'Parent comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
    ]);
    
    // Create reply
    $reply = Comment::create([
        'content' => 'Reply comment',
        'user_id' => $user->id,
        'commentable_type' => Post::class,
        'commentable_id' => $post->id,
        'parent_id' => $parentComment->id,
    ]);
    
    $response = $this->actingAs($user)->getJson("/posts/{$post->id}/comments?sort=newest");
    $response->assertStatus(200);
    
    $comments = $response->json('comments');
    expect($comments)->toHaveCount(1); // Only parent comments
    expect($comments[0]['replies'])->toHaveCount(1); // Reply included
    expect($comments[0]['replies'][0]['id'])->toBe($reply->id);
    expect($comments[0]['replies'][0]['likes_count'])->toBe(0);
    expect($comments[0]['replies'][0]['is_liked_by_user'])->toBe(false);
});
