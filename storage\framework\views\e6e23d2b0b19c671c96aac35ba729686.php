<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['target', 'targetType' => 'post', 'currentReaction' => null, 'reactionCounts' => []]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['target', 'targetType' => 'post', 'currentReaction' => null, 'reactionCounts' => []]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    // Get current user's reaction
    $userReaction = null;
    if (auth()->check()) {
        $userReaction = $target->reactions()->where('user_id', auth()->id())->first();
    }
    
    // Get reaction details if user has reacted
    $reactionDetails = null;
    if ($userReaction) {
        $reactionDetails = \App\Models\Reaction::getReactionDetails($userReaction->type);
    }
    
    // Calculate total reactions
    $totalReactions = $target->reactions()->count();
?>

<div class="reaction-wrapper relative inline-block">
    <!-- Main Reaction Button -->
    <button class="reaction-btn flex items-center space-x-2 <?php echo e($reactionDetails ? $reactionDetails['color'] : 'text-gray-500'); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
            data-target-id="<?php echo e($target->id); ?>"
            data-target-type="<?php echo e($targetType); ?>"
            data-current-reaction="<?php echo e($userReaction ? $userReaction->type : ''); ?>">
        
        <?php if($reactionDetails): ?>
            <!-- User has reacted - show their reaction -->
            <img src="<?php echo e($reactionDetails['emoji']); ?>" alt="<?php echo e($reactionDetails['label']); ?>" class="w-5 h-5 reaction-emoji"
                 onerror="this.style.display='none';">
            <span class="text-sm font-medium"><?php echo e($reactionDetails['label']); ?></span>
        <?php else: ?>
            <!-- Default like button -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
            </svg>
            <span class="text-sm font-medium">Like</span>
        <?php endif; ?>
    </button>
    
    <!-- Reaction Count (if any) -->
    <?php if($totalReactions > 0): ?>
        <span class="reaction-count text-xs text-gray-500 ml-1"><?php echo e($totalReactions); ?></span>
    <?php endif; ?>
    
    <!-- Reaction Popup (will be created by JavaScript) -->
</div>

<!-- CSS for Facebook-style reactions -->
<style>
/* Reaction popup animations */
.reaction-popup {
    pointer-events: none;
    transform-origin: bottom left;
    z-index: 9999 !important;
    position: absolute !important;
    bottom: 100% !important;
    left: 0 !important;
    margin-bottom: 0.5rem !important;
    white-space: nowrap;
}

.reaction-popup.opacity-100 {
    pointer-events: auto;
}

/* Ensure wrapper has relative positioning */
.reaction-wrapper {
    position: relative !important;
    display: inline-block;
}

/* Alternative positioning for popups near viewport edges */
.reaction-popup.position-top {
    bottom: auto !important;
    top: 100% !important;
    margin-bottom: 0 !important;
    margin-top: 0.5rem !important;
    transform-origin: top left;
}

/* Ensure popup doesn't get cut off */
.reaction-popup {
    min-width: max-content;
}

/* Force popup to be visible above all content */
.reaction-wrapper:hover {
    z-index: 9999;
    position: relative;
}

/* Ensure parent containers don't clip the popup */
.reaction-wrapper {
    overflow: visible !important;
}

/* Additional positioning fixes */
.reaction-popup {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    background: white !important;
}

/* Emoji image styling */
.reaction-emoji {
    display: inline-block;
    vertical-align: middle;
    object-fit: contain;
    transition: transform 0.2s ease;
    border-radius: 4px;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

.reaction-option .reaction-emoji {
    width: 2rem;
    height: 2rem;
    filter: brightness(1);
    transition: filter 0.2s ease, transform 0.2s ease;
}

.reaction-option:hover .reaction-emoji {
    filter: brightness(1.1);
}

.reaction-btn .reaction-emoji {
    width: 1.25rem;
    height: 1.25rem;
}

/* Ensure GIFs play smoothly */
.reaction-emoji {
    will-change: transform;
    backface-visibility: hidden;
}

/* Reaction option hover effects */
.reaction-option {
    position: relative;
    overflow: hidden;
}

.reaction-option::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.reaction-option:hover::before {
    width: 100%;
    height: 100%;
}

/* Reaction button active state */
.reaction-btn[data-current-reaction]:not([data-current-reaction=""]) {
    background-color: rgba(59, 130, 246, 0.1);
}

/* Smooth emoji animations */
.reaction-option img:first-child,
.reaction-option span:first-child {
    transition: transform 0.2s ease;
}

.reaction-option:hover img:first-child,
.reaction-option:hover span:first-child {
    transform: scale(1.2);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .reaction-popup {
        transform-origin: bottom center;
        left: 50% !important;
        transform: translateX(-50%) scale(0.95);
        bottom: 100% !important;
        margin-bottom: 0.75rem !important;
    }

    .reaction-popup.opacity-100 {
        transform: translateX(-50%) scale(1);
    }

    .reaction-popup.position-top {
        top: 100% !important;
        bottom: auto !important;
        margin-top: 0.75rem !important;
        margin-bottom: 0 !important;
        transform-origin: top center;
    }
    
    .reaction-option {
        padding: 0.75rem;
    }

    .reaction-option .reaction-emoji {
        width: 1.75rem;
        height: 1.75rem;
    }

    .reaction-option span:first-child {
        font-size: 1.75rem;
    }
}

/* Accessibility improvements */
.reaction-option:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

.reaction-btn:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* Loading state */
.reaction-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Reaction count badge */
.reaction-count {
    display: inline-block;
    min-width: 1rem;
    text-align: center;
}

/* Pulse animation for new reactions */
@keyframes reactionPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.reaction-btn.pulse {
    animation: reactionPulse 0.3s ease-in-out;
}

/* Reaction popup shadow and border */
.reaction-popup {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reaction-popup {
        background-color: #374151;
        border-color: #4B5563;
    }
    
    .reaction-option:hover {
        background-color: #4B5563;
    }
    
    .reaction-option span:last-child {
        color: #D1D5DB;
    }
}
</style>

<!-- Initialize reactions for this component -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure the reaction system is initialized
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }
});
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/facebook-reactions.blade.php ENDPATH**/ ?>