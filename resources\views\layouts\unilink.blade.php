<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        @auth
        <meta name="user-id" content="{{ auth()->id() }}">
        <meta name="user-role" content="{{ auth()->user()->role }}">
        @endauth

        <title>{{ config('app.name', 'UniLink') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased bg-gray-50 overflow-hidden">
        <!-- Header Navigation -->
        @include('layouts.unilink-header')

        <!-- Main 3-Column Container -->
        <div class="flex h-screen pt-16">
            <!-- Left Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-64 xl:w-72 bg-white border-r border-custom-second-darkest overflow-y-auto flex-shrink-0">
                @include('layouts.unilink-sidebar-content')
            </div>

            <!-- Central Content -->
            <main class="flex-1 bg-gray-50 overflow-y-auto min-w-0">
                <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    {{ $slot }}
                </div>
            </main>

            <!-- Right Sidebar -->
            <div class="hidden lg:flex lg:flex-col lg:w-80 xl:w-96 bg-white border-l border-custom-second-darkest overflow-y-auto flex-shrink-0">
                @include('layouts.unilink-right-sidebar-content')
            </div>
        </div>

        <!-- Mobile Sidebar -->
        <div class="lg:hidden">
            @include('layouts.unilink-sidebar')
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-data="{ open: false }"
             x-on:toggle-sidebar.window="open = !open"
             x-show="open"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-custom-darkest bg-opacity-75 z-30 lg:hidden"
             @click="$dispatch('toggle-sidebar')">
        </div>

        <!-- Notification Popup -->
        @include('components.notification-popup')
    </body>
</html>
