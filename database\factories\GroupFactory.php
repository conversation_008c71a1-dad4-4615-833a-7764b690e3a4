<?php

namespace Database\Factories;

use App\Models\Group;
use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Group>
 */
class GroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(2, true);

        return [
            'name' => $name,
            'slug' => Group::generateSlug($name),
            'description' => $this->faker->paragraph(),
            'visibility' => $this->faker->randomElement(['public', 'private']),
            'post_approval' => $this->faker->randomElement(['none', 'required']),
            'allow_file_sharing' => $this->faker->boolean(80),
            'allowed_file_types' => ['pdf', 'jpg', 'png', 'doc'],
            'max_file_size_mb' => $this->faker->numberBetween(5, 20),
            'status' => 'active',
            'created_by' => User::factory(),
            'organization_id' => $this->faker->boolean(60) ? Organization::factory() : null,
        ];
    }

    /**
     * Indicate that the group is public.
     */
    public function public(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => 'public',
        ]);
    }

    /**
     * Indicate that the group is private.
     */
    public function private(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => 'private',
        ]);
    }

    /**
     * Indicate that the group requires post approval.
     */
    public function withPostApproval(): static
    {
        return $this->state(fn (array $attributes) => [
            'post_approval' => 'required',
        ]);
    }
}
