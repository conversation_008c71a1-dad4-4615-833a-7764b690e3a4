<?php

namespace App\Livewire;

use App\Models\Organization;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class OrganizationFollower extends Component
{
    public Organization $organization;
    public $isFollowing = false;
    public $followersCount = 0;
    public $isLoading = false;

    public function mount(Organization $organization)
    {
        $this->organization = $organization;
        $this->loadFollowStatus();
        $this->loadFollowersCount();
    }

    public function loadFollowStatus()
    {
        if (Auth::check()) {
            $this->isFollowing = $this->organization->isFollowedBy(Auth::user());
        }
    }

    public function loadFollowersCount()
    {
        $this->followersCount = $this->organization->followers()->count();
    }

    public function toggleFollow()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->isLoading = true;

        try {
            $user = Auth::user();

            if ($this->isFollowing) {
                // Unfollow
                $this->organization->followers()->detach($user->id);
                $this->isFollowing = false;
                $this->followersCount--;
                session()->flash('success', 'You have unfollowed this organization.');
            } else {
                // Follow
                $this->organization->followers()->attach($user->id);
                $this->isFollowing = true;
                $this->followersCount++;
                session()->flash('success', 'You are now following this organization!');
            }

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while updating your follow status.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.organization-follower');
    }
}
