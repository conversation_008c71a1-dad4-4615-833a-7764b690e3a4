<?php

namespace App\Livewire;

use App\Models\Post;
use App\Models\Group;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class PostApproval extends Component
{
    public Post $post;
    public Group $group;
    public $isLoading = false;

    public function mount(Post $post, Group $group)
    {
        $this->post = $post;
        $this->group = $group;
    }

    public function approvePost()
    {
        if (!$this->group->userCanModerate(Auth::user())) {
            session()->flash('error', 'You do not have permission to approve posts.');
            return;
        }

        $this->isLoading = true;

        try {
            $this->post->update([
                'approval_status' => 'approved',
                'approved_at' => now(),
                'approved_by' => Auth::id(),
            ]);

            session()->flash('success', 'Post approved successfully!');
            $this->dispatch('postApproved', $this->post->id);

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while approving the post.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function rejectPost()
    {
        if (!$this->group->userCanModerate(Auth::user())) {
            session()->flash('error', 'You do not have permission to reject posts.');
            return;
        }

        $this->isLoading = true;

        try {
            $this->post->update([
                'approval_status' => 'rejected',
                'approved_by' => Auth::id(),
            ]);

            session()->flash('success', 'Post rejected.');
            $this->dispatch('postRejected', $this->post->id);

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while rejecting the post.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.post-approval');
    }
}
