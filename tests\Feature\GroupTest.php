<?php

use App\Models\Group;
use App\Models\Organization;
use App\Models\User;

test('user can create group', function () {
    $user = User::factory()->create();
    $organization = Organization::factory()->create();

    $response = $this->actingAs($user)->post('/groups', [
        'name' => 'Test Group',
        'description' => 'A test group',
        'organization_id' => $organization->id,
        'visibility' => 'public',
        'post_approval' => 'none',
        'allow_file_sharing' => true,
        'max_file_size_mb' => 10,
    ]);

    $response->assertRedirect();
    $this->assertDatabaseHas('groups', [
        'name' => 'Test Group',
        'created_by' => $user->id,
    ]);
});

test('user can join public group', function () {
    $creator = User::factory()->create();
    $user = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'public'
    ]);

    $response = $this->actingAs($user)->post("/groups/{$group->slug}/join");

    $response->assertRedirect();
    expect($group->hasActiveMember($user))->toBeTrue();
});

test('user can request to join private group', function () {
    $creator = User::factory()->create();
    $user = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
        'visibility' => 'private'
    ]);

    $response = $this->actingAs($user)->post("/groups/{$group->slug}/join");

    $response->assertRedirect();
    expect($group->hasMember($user))->toBeTrue();

    $membership = $group->members()->where('user_id', $user->id)->first();
    expect($membership->pivot->status)->toBe('pending');
});

test('group creator cannot leave group', function () {
    $creator = User::factory()->create();

    $group = Group::factory()->create([
        'created_by' => $creator->id,
    ]);

    $response = $this->actingAs($creator)
        ->delete("/groups/{$group->slug}/leave");

    $response->assertRedirect();
    $response->assertSessionHas('error');
});
