<?php

namespace Database\Seeders;

use App\Models\Post;
use App\Models\User;
use App\Models\Organization;
use Illuminate\Database\Seeder;

class PostSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create a test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => bcrypt('password'),
                'role' => 'student',
                'student_id' => 'STU001',
                'email_verified_at' => now(),
            ]
        );

        // Get the first organization if it exists
        $organization = Organization::first();

        // Create sample posts
        $posts = [
            [
                'title' => 'Welcome to UniLink!',
                'content' => 'This is a sample post to demonstrate the post creation feature. You can create posts, add images, and interact with the community!',
                'type' => 'general',
                'user_id' => $user->id,
                'organization_id' => null,
                'status' => 'published',
                'published_at' => now(),
            ],
            [
                'title' => 'Upcoming Campus Event',
                'content' => 'Join us for an exciting campus event next week! There will be food, music, and great networking opportunities. Don\'t miss out on this amazing experience.',
                'type' => 'event',
                'user_id' => $user->id,
                'organization_id' => $organization?->id,
                'status' => 'published',
                'published_at' => now()->subHours(2),
            ],
            [
                'title' => 'Important Announcement',
                'content' => 'Please note that the library will be closed for maintenance this weekend. We apologize for any inconvenience this may cause.',
                'type' => 'announcement',
                'user_id' => $user->id,
                'organization_id' => null,
                'status' => 'published',
                'published_at' => now()->subHours(5),
            ],
            [
                'title' => 'Study Group Formation',
                'content' => 'Looking to form a study group for the upcoming midterm exams. If you\'re interested in collaborative learning and sharing knowledge, please reach out!',
                'type' => 'general',
                'user_id' => $user->id,
                'organization_id' => null,
                'status' => 'published',
                'published_at' => now()->subDay(),
            ],
        ];

        foreach ($posts as $postData) {
            Post::create($postData);
        }

        $this->command->info('Sample posts created successfully!');
    }
}
