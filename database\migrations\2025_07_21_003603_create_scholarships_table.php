<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('scholarships', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->decimal('amount', 10, 2)->nullable();
            $table->string('provider');
            $table->text('requirements');
            $table->text('application_process');
            $table->string('application_url')->nullable();
            $table->date('deadline');
            $table->json('eligibility_criteria')->nullable();
            $table->json('required_documents')->nullable();
            $table->enum('status', ['active', 'expired', 'draft'])->default('active');
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('scholarships');
    }
};
