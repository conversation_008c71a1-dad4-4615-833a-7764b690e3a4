<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class Attachment extends Model
{
    protected $fillable = [
        'post_id',
        'filename',
        'original_filename',
        'file_path',
        'file_size',
        'mime_type',
        'file_type',
    ];

    /**
     * Get the post this attachment belongs to
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class);
    }

    /**
     * Get the file URL
     */
    public function getUrlAttribute(): string
    {
        return Storage::disk('public')->url($this->file_path);
    }

    /**
     * Get human readable file size
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage(): bool
    {
        return in_array($this->file_type, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }

    /**
     * Check if file is a PDF
     */
    public function isPdf(): bool
    {
        return $this->file_type === 'pdf';
    }

    /**
     * Check if file is a document
     */
    public function isDocument(): bool
    {
        return in_array($this->file_type, ['pdf', 'doc', 'docx', 'txt', 'rtf']);
    }

    /**
     * Get file icon based on type
     */
    public function getIconAttribute(): string
    {
        $iconMap = [
            'pdf' => 'document-text',
            'doc' => 'document-text',
            'docx' => 'document-text',
            'txt' => 'document-text',
            'rtf' => 'document-text',
            'jpg' => 'photograph',
            'jpeg' => 'photograph',
            'png' => 'photograph',
            'gif' => 'photograph',
            'webp' => 'photograph',
            'mp4' => 'film',
            'avi' => 'film',
            'mov' => 'film',
            'zip' => 'archive',
            'rar' => 'archive',
            '7z' => 'archive',
        ];

        return $iconMap[$this->file_type] ?? 'document';
    }

    /**
     * Delete the physical file when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($attachment) {
            if (Storage::disk('public')->exists($attachment->file_path)) {
                Storage::disk('public')->delete($attachment->file_path);
            }
        });
    }
}
