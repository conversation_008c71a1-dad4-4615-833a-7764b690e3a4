import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                'custom': {
                    'lightest': '#EEEEEE',      // Light Gray
                    'green': '#7BC74D',         // Green
                    'dark-gray': '#393E46',     // Dark Gray
                    'darkest': '#222831',       // Dark Navy
                }
            },
        },
    },

    plugins: [forms],
};
