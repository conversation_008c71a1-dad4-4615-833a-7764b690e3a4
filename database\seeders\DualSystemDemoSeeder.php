<?php

namespace Database\Seeders;

use App\Models\Group;
use App\Models\Organization;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DualSystemDemoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create demo users
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Demo Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'admin'
            ]
        );

        $student1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'student'
            ]
        );

        $student2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role' => 'student'
            ]
        );

        // Create demo organizations
        $studentCouncil = Organization::firstOrCreate(
            ['slug' => 'student-council'],
            [
                'name' => 'Student Council',
                'description' => 'Official student government representing all students',
                'about' => 'The Student Council serves as the voice of the student body, organizing events, addressing concerns, and fostering a vibrant campus community.',
                'founded_date' => '2020-01-15',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'website' => 'https://university.edu/student-council',
                'status' => 'active',
                'is_page_mode' => true,
                'allow_public_posts' => false,
                'created_by' => $admin->id,
            ]
        );

        $techClub = Organization::firstOrCreate(
            ['slug' => 'tech-innovators-club'],
            [
                'name' => 'Tech Innovators Club',
                'description' => 'Exploring cutting-edge technology and innovation',
                'about' => 'A community of tech enthusiasts working on innovative projects, hosting workshops, and connecting with industry professionals.',
                'founded_date' => '2021-09-01',
                'email' => '<EMAIL>',
                'status' => 'active',
                'is_page_mode' => true,
                'allow_public_posts' => false,
                'created_by' => $student1->id,
            ]
        );

        // Add members to organizations
        $studentCouncil->members()->syncWithoutDetaching([
            $admin->id => ['role' => 'president', 'status' => 'active', 'joined_at' => now()],
            $student1->id => ['role' => 'officer', 'status' => 'active', 'joined_at' => now()],
        ]);

        $techClub->members()->syncWithoutDetaching([
            $student1->id => ['role' => 'president', 'status' => 'active', 'joined_at' => now()],
            $student2->id => ['role' => 'officer', 'status' => 'active', 'joined_at' => now()],
        ]);

        // Create demo groups
        $studyGroup = Group::firstOrCreate(
            ['slug' => 'cs-study-group'],
            [
                'name' => 'Computer Science Study Group',
                'description' => 'Collaborative learning space for CS students to share resources, discuss assignments, and prepare for exams together.',
                'visibility' => 'public',
                'post_approval' => 'none',
                'allow_file_sharing' => true,
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'jpg', 'png'],
                'max_file_size_mb' => 10,
                'status' => 'active',
                'created_by' => $student1->id,
                'organization_id' => $techClub->id,
            ]
        );

        $projectGroup = Group::firstOrCreate(
            ['slug' => 'hackathon-team-alpha'],
            [
                'name' => 'Hackathon Team Alpha',
                'description' => 'Private group for our hackathon team to coordinate, share code, and plan our winning strategy!',
                'visibility' => 'private',
                'post_approval' => 'none',
                'allow_file_sharing' => true,
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'jpg', 'png', 'zip'],
                'max_file_size_mb' => 25,
                'status' => 'active',
                'created_by' => $student2->id,
            ]
        );

        // Add members to groups
        $studyGroup->members()->syncWithoutDetaching([
            $student1->id => ['role' => 'admin', 'status' => 'active', 'joined_at' => now()],
            $student2->id => ['role' => 'member', 'status' => 'active', 'joined_at' => now()],
            $admin->id => ['role' => 'member', 'status' => 'active', 'joined_at' => now()],
        ]);

        $projectGroup->members()->syncWithoutDetaching([
            $student2->id => ['role' => 'admin', 'status' => 'active', 'joined_at' => now()],
            $student1->id => ['role' => 'member', 'status' => 'active', 'joined_at' => now()],
        ]);

        // Create demo posts for organizations
        Post::firstOrCreate(
            ['title' => 'Welcome to the New Academic Year!'],
            [
                'content' => 'Dear students, welcome back! We\'re excited to announce several new initiatives this year including improved student services, new study spaces, and enhanced campus activities. Stay tuned for more updates!',
                'type' => 'announcement',
                'status' => 'published',
                'approval_status' => 'approved',
                'user_id' => $admin->id,
                'organization_id' => $studentCouncil->id,
                'published_at' => now()->subDays(2),
                'approved_at' => now()->subDays(2),
                'approved_by' => $admin->id,
            ]
        );

        Post::firstOrCreate(
            ['title' => 'Tech Workshop: AI & Machine Learning'],
            [
                'content' => 'Join us for an exciting workshop on AI and Machine Learning fundamentals! We\'ll cover practical applications, hands-on coding sessions, and industry insights. Date: Next Friday, 2 PM in Tech Lab 101.',
                'type' => 'event',
                'status' => 'published',
                'approval_status' => 'approved',
                'user_id' => $student1->id,
                'organization_id' => $techClub->id,
                'published_at' => now()->subDays(1),
                'approved_at' => now()->subDays(1),
                'approved_by' => $student1->id,
            ]
        );

        // Create demo posts for groups
        Post::firstOrCreate(
            ['title' => 'Study Session: Data Structures & Algorithms'],
            [
                'content' => 'Hey everyone! Let\'s organize a study session for the upcoming DSA exam. I\'ve prepared some practice problems and we can work through them together. Who\'s interested?',
                'type' => 'general',
                'status' => 'published',
                'approval_status' => 'approved',
                'user_id' => $student1->id,
                'group_id' => $studyGroup->id,
                'published_at' => now()->subHours(6),
                'approved_at' => now()->subHours(6),
                'approved_by' => $student1->id,
            ]
        );

        Post::firstOrCreate(
            ['title' => 'Project Update: MVP Ready for Testing'],
            [
                'content' => 'Great news team! Our MVP is ready for initial testing. I\'ve uploaded the latest build to our shared folder. Please test the core features and report any bugs in our tracking sheet.',
                'type' => 'general',
                'status' => 'published',
                'approval_status' => 'approved',
                'user_id' => $student2->id,
                'group_id' => $projectGroup->id,
                'published_at' => now()->subHours(3),
                'approved_at' => now()->subHours(3),
                'approved_by' => $student2->id,
            ]
        );

        // Add some followers to organizations
        $studentCouncil->followers()->syncWithoutDetaching([$student1->id, $student2->id]);
        $techClub->followers()->syncWithoutDetaching([$student2->id, $admin->id]);

        $this->command->info('Demo data created successfully!');
        $this->command->info('Demo users created:');
        $this->command->info('- <EMAIL> (password: password)');
        $this->command->info('- <EMAIL> (password: password)');
        $this->command->info('- <EMAIL> (password: password)');
    }
}
