<?php

namespace App\Http\Controllers;

use App\Models\Scholarship;
use Illuminate\Http\Request;

class ScholarshipController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Scholarship::with('creator')
            ->active()
            ->notExpired();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('provider', 'like', "%{$search}%");
            });
        }

        // Filter by amount range
        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->min_amount);
        }
        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->max_amount);
        }

        // Filter by deadline
        if ($request->filled('deadline_filter')) {
            switch ($request->deadline_filter) {
                case 'this_month':
                    $query->whereMonth('deadline', now()->month)
                          ->whereYear('deadline', now()->year);
                    break;
                case 'next_month':
                    $query->whereMonth('deadline', now()->addMonth()->month)
                          ->whereYear('deadline', now()->addMonth()->year);
                    break;
                case 'this_year':
                    $query->whereYear('deadline', now()->year);
                    break;
            }
        }

        $scholarships = $query->orderBy('deadline', 'asc')
                             ->paginate(12);

        return view('scholarships.index', compact('scholarships'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('scholarships.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'amount' => 'nullable|numeric|min:0|max:999999.99',
            'provider' => 'required|string|max:255',
            'requirements' => 'required|string|max:2000',
            'application_process' => 'required|string|max:2000',
            'application_url' => 'nullable|url|max:255',
            'deadline' => 'required|date|after:today',
            'eligibility_criteria' => 'nullable|array',
            'eligibility_criteria.*' => 'string|max:255',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'string|max:255',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['status'] = 'active';

        // Convert arrays to proper format
        if (isset($validated['eligibility_criteria'])) {
            $validated['eligibility_criteria'] = array_filter($validated['eligibility_criteria']);
        }
        if (isset($validated['required_documents'])) {
            $validated['required_documents'] = array_filter($validated['required_documents']);
        }

        $scholarship = Scholarship::create($validated);

        return redirect()->route('scholarships.show', $scholarship)
            ->with('success', 'Scholarship created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Scholarship $scholarship)
    {
        $scholarship->load(['creator', 'comments.user', 'likes']);

        return view('scholarships.show', compact('scholarship'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Scholarship $scholarship)
    {
        // Check if user can edit this scholarship
        if (!$this->canManageScholarship($scholarship)) {
            abort(403, 'Unauthorized to edit this scholarship.');
        }

        return view('scholarships.edit', compact('scholarship'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Scholarship $scholarship)
    {
        // Check if user can edit this scholarship
        if (!$this->canManageScholarship($scholarship)) {
            abort(403, 'Unauthorized to update this scholarship.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'amount' => 'nullable|numeric|min:0|max:999999.99',
            'provider' => 'required|string|max:255',
            'requirements' => 'required|string|max:2000',
            'application_process' => 'required|string|max:2000',
            'application_url' => 'nullable|url|max:255',
            'deadline' => 'required|date|after:today',
            'eligibility_criteria' => 'nullable|array',
            'eligibility_criteria.*' => 'string|max:255',
            'required_documents' => 'nullable|array',
            'required_documents.*' => 'string|max:255',
        ]);

        // Convert arrays to proper format
        if (isset($validated['eligibility_criteria'])) {
            $validated['eligibility_criteria'] = array_filter($validated['eligibility_criteria']);
        }
        if (isset($validated['required_documents'])) {
            $validated['required_documents'] = array_filter($validated['required_documents']);
        }

        $scholarship->update($validated);

        return redirect()->route('scholarships.show', $scholarship)
            ->with('success', 'Scholarship updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Scholarship $scholarship)
    {
        // Check if user can delete this scholarship
        if (!$this->canManageScholarship($scholarship)) {
            abort(403, 'Unauthorized to delete this scholarship.');
        }

        $scholarship->delete();

        return redirect()->route('scholarships.index')
            ->with('success', 'Scholarship deleted successfully!');
    }

    /**
     * Check if user can manage scholarship
     */
    private function canManageScholarship(Scholarship $scholarship): bool
    {
        $user = auth()->user();

        // Admins can manage any scholarship
        if ($user->isAdmin()) {
            return true;
        }

        // Scholarship creator can manage
        if ($scholarship->created_by === $user->id) {
            return true;
        }

        // Organization officers can manage if they have management access
        if ($user->hasManagementAccess()) {
            return true;
        }

        return false;
    }
}
