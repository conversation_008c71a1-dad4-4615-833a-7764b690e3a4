@props(['target', 'targetType' => 'post', 'currentReaction' => null, 'reactionCounts' => []])

@php
    // Get current user's reaction
    $userReaction = null;
    if (auth()->check()) {
        $userReaction = $target->reactions()->where('user_id', auth()->id())->first();
    }
    
    // Get reaction details if user has reacted
    $reactionDetails = null;
    if ($userReaction) {
        $reactionDetails = \App\Models\Reaction::getReactionDetails($userReaction->type);
    }
    
    // Calculate total reactions
    $totalReactions = $target->reactions()->count();
@endphp

<div class="reaction-wrapper relative inline-block">
    <!-- Main Reaction Button -->
    <button class="reaction-btn flex items-center space-x-2 {{ $reactionDetails ? $reactionDetails['color'] : 'text-gray-500' }} transition-colors duration-200 py-2 px-3 rounded-lg"
            data-target-id="{{ $target->id }}"
            data-target-type="{{ $targetType }}"
            data-current-reaction="{{ $userReaction ? $userReaction->type : '' }}">
        
        @if($reactionDetails)
            <!-- User has reacted - show their reaction -->
            <img src="{{ $reactionDetails['emoji'] }}" alt="{{ $reactionDetails['label'] }}" class="w-5 h-5 reaction-emoji"
                 onerror="this.style.display='none';">
            <span class="text-sm font-medium">{{ $reactionDetails['label'] }}</span>
        @else
            <!-- Default like button -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
            </svg>
            <span class="text-sm font-medium">Like</span>
        @endif
    </button>
    
    <!-- Reaction Count (if any) -->
    @if($totalReactions > 0)
        <span class="reaction-count text-xs text-gray-500 ml-1">{{ $totalReactions }}</span>
    @endif
    
    <!-- Reaction Popup (will be created by JavaScript) -->
</div>

<!-- CSS for Facebook-style reactions -->
<style>
/* Reaction popup animations */
.reaction-popup {
    pointer-events: none;
    transform-origin: bottom left;
    z-index: 9999 !important;
    position: absolute !important;
    bottom: 100% !important;
    left: 0 !important;
    margin-bottom: 0.25rem !important;
    white-space: nowrap;
}

/* Create invisible bridge between button and popup to maintain hover */
.reaction-popup::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 0.25rem;
    background: transparent;
    pointer-events: auto;
}

.reaction-popup.opacity-100 {
    pointer-events: auto;
}

/* Keep popup visible when hovering over wrapper or popup */
.reaction-wrapper:hover .reaction-popup.opacity-100,
.reaction-popup:hover {
    opacity: 1 !important;
    visibility: visible !important;
    transform: scale(1) !important;
    pointer-events: auto !important;
}

/* Ensure wrapper has relative positioning */
.reaction-wrapper {
    position: relative !important;
    display: inline-block;
}

/* Alternative positioning for popups near viewport edges */
.reaction-popup.position-top {
    bottom: auto !important;
    top: 100% !important;
    margin-bottom: 0 !important;
    margin-top: 0.5rem !important;
    transform-origin: top left;
}

/* Ensure popup doesn't get cut off */
.reaction-popup {
    min-width: max-content;
}

/* Force popup to be visible above all content */
.reaction-wrapper:hover {
    z-index: 9999;
    position: relative;
}

/* Ensure parent containers don't clip the popup */
.reaction-wrapper {
    overflow: visible !important;
}

/* Popup container positioning */
.reaction-popup-container {
    position: absolute;
    bottom: 100%;
    left: 0;
    margin-bottom: 0.5rem;
    z-index: 9999;
}

/* Additional positioning fixes */
.reaction-popup {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    border: 1px solid rgba(0, 0, 0, 0.08) !important;
    background: white !important;
    backdrop-filter: blur(10px);
}

/* Emoji image styling */
.reaction-emoji {
    display: inline-block;
    vertical-align: middle;
    object-fit: cover;
    transition: transform 0.2s ease;
    border-radius: 50%;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    background: transparent;
    overflow: hidden;
}

.reaction-option .reaction-emoji {
    width: 2rem;
    height: 2rem;
    filter: brightness(1);
    transition: filter 0.2s ease, transform 0.2s ease;
    border-radius: 50%;
    border: 2px solid transparent;
    background-clip: padding-box;
}

/* .reaction-option:hover .reaction-emoji {
    filter: brightness(1.1);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
} */

.reaction-btn .reaction-emoji {
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    border: 1px solid transparent;
    background-clip: padding-box;
}

/* Ensure GIFs play smoothly */
.reaction-emoji {
    will-change: transform;
    backface-visibility: hidden;
}

/* Reaction option hover effects */
.reaction-option {
    position: relative;
    overflow: hidden;
    border-radius: 50%;
    transition: background-color 0.2s ease, transform 0.2s ease;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.reaction-option:hover {
    background-color: transparent!important;
    transform: scale(1.50);
}

/* Floating reaction label styling */
.floating-reaction-label {
    pointer-events: none;
    font-weight: 500;
    letter-spacing: 0.025em;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 0.25rem;
    z-index: 30;
}

.floating-reaction-label::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #374151;
}

/* Reaction button active state */
.reaction-btn[data-current-reaction]:not([data-current-reaction=""]) {
    background-color: rgba(33, 247, 48, 0.1);
}

/* Smooth emoji animations */
.reaction-option img:first-child,
.reaction-option span:first-child {
    transition: transform 0.2s ease;
}

.reaction-option:hover img:first-child,
.reaction-option:hover span:first-child {
    transform: scale(1.2);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .reaction-popup {
        transform-origin: bottom center;
        left: 50% !important;
        transform: translateX(-50%) scale(0.95);
        bottom: 100% !important;
        margin-bottom: 0.75rem !important;
    }

    .reaction-popup.opacity-100 {
        transform: translateX(-50%) scale(1);
    }

    .reaction-popup.position-top {
        top: 100% !important;
        bottom: auto !important;
        margin-top: 0.25rem !important;
        margin-bottom: 0 !important;
        transform-origin: top center;
    }

    .reaction-popup.position-top::after {
        top: -0.25rem;
        bottom: auto;
    }
    
    .reaction-option {
        width: 2.5rem;
        height: 2.5rem;
    }

    .reaction-option .reaction-emoji {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        border: 2px solid transparent;
    }

    /* Mobile floating label adjustments */
    .floating-reaction-label {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    .reaction-option span:first-child {
        font-size: 1.75rem;
    }
}

/* Accessibility improvements */
.reaction-option:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

.reaction-btn:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* Loading state */
.reaction-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Reaction count badge */
.reaction-count {
    display: inline-block;
    min-width: 1rem;
    text-align: center;
}

/* Pulse animation for new reactions */
@keyframes reactionPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.reaction-btn.pulse {
    animation: reactionPulse 0.3s ease-in-out;
}

/* Reaction popup shadow and border */
.reaction-popup {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reaction-popup {
        background-color: #374151;
        border-color: #4B5563;
    }
    
    .reaction-option:hover {
        background-color: #4B5563;
    }
    
    .reaction-option span:last-child {
        color: #D1D5DB;
    }
}
</style>

<!-- Initialize reactions for this component -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure the reaction system is initialized
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }
});
</script>
