<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Organization>
 */
class OrganizationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->company();

        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => $this->faker->paragraph(),
            'about' => $this->faker->paragraph(3),
            'founded_date' => $this->faker->date(),
            'email' => $this->faker->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'website' => $this->faker->url(),
            'social_links' => [
                'facebook' => $this->faker->url(),
                'twitter' => $this->faker->url(),
            ],
            'contact_info' => [
                'address' => $this->faker->address(),
                'office_hours' => '9 AM - 5 PM',
            ],
            'status' => 'active',
            'is_page_mode' => $this->faker->boolean(30),
            'allow_public_posts' => $this->faker->boolean(20),
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the organization is in page mode.
     */
    public function pageMode(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_page_mode' => true,
        ]);
    }

    /**
     * Indicate that the organization allows public posts.
     */
    public function allowPublicPosts(): static
    {
        return $this->state(fn (array $attributes) => [
            'allow_public_posts' => true,
        ]);
    }
}
