<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Find Scholarships</h1>
                <p class="text-gray-600 mt-1">Discover financial aid opportunities for your education</p>
            </div>
            @if(auth()->user()->hasManagementAccess())
                <a href="{{ route('scholarships.create') }}" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                    Add Scholarship
                </a>
            @endif
        </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <form method="GET" action="{{ route('scholarships.index') }}" class="space-y-4">
            <!-- Search -->
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Scholarships</label>
                <input type="text" id="search" name="search" value="{{ request('search') }}" 
                       placeholder="Search by title, description, or provider..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Amount Range -->
                <div>
                    <label for="min_amount" class="block text-sm font-medium text-gray-700 mb-1">Min Amount</label>
                    <input type="number" id="min_amount" name="min_amount" value="{{ request('min_amount') }}" 
                           placeholder="$0" min="0" step="100"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <div>
                    <label for="max_amount" class="block text-sm font-medium text-gray-700 mb-1">Max Amount</label>
                    <input type="number" id="max_amount" name="max_amount" value="{{ request('max_amount') }}" 
                           placeholder="$50,000" min="0" step="100"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                </div>
                
                <!-- Deadline Filter -->
                <div>
                    <label for="deadline_filter" class="block text-sm font-medium text-gray-700 mb-1">Deadline</label>
                    <select id="deadline_filter" name="deadline_filter" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <option value="">All Deadlines</option>
                        <option value="this_month" {{ request('deadline_filter') === 'this_month' ? 'selected' : '' }}>This Month</option>
                        <option value="next_month" {{ request('deadline_filter') === 'next_month' ? 'selected' : '' }}>Next Month</option>
                        <option value="this_year" {{ request('deadline_filter') === 'this_year' ? 'selected' : '' }}>This Year</option>
                    </select>
                </div>
                
                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        Search
                    </button>
                </div>
            </div>
            
            @if(request()->hasAny(['search', 'min_amount', 'max_amount', 'deadline_filter']))
                <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                    <p class="text-sm text-gray-600">{{ $scholarships->total() }} scholarships found</p>
                    <a href="{{ route('scholarships.index') }}" class="text-sm text-blue-600 hover:text-blue-800">Clear filters</a>
                </div>
            @endif
        </form>
    </div>

    <!-- Scholarships Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($scholarships as $scholarship)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Header -->
                <div class="p-4 border-b border-gray-200">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-900 line-clamp-2">{{ $scholarship->title }}</h3>
                        @if($scholarship->amount)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800 ml-2 flex-shrink-0">
                                ${{ number_format($scholarship->amount) }}
                            </span>
                        @endif
                    </div>
                    
                    <p class="text-sm text-gray-600 mb-2">{{ $scholarship->provider }}</p>
                    
                    <!-- Deadline -->
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                        </svg>
                        Deadline: {{ $scholarship->deadline->format('M j, Y') }}
                        @php
                            $daysLeft = now()->diffInDays($scholarship->deadline, false);
                        @endphp
                        @if($daysLeft <= 7 && $daysLeft >= 0)
                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                {{ $daysLeft }} days left
                            </span>
                        @elseif($daysLeft <= 30 && $daysLeft > 7)
                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                {{ $daysLeft }} days left
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Content -->
                <div class="p-4">
                    <p class="text-gray-700 text-sm mb-4 line-clamp-3">{{ Str::limit($scholarship->description, 150) }}</p>
                    
                    <!-- Requirements Preview -->
                    @if($scholarship->eligibility_criteria && count($scholarship->eligibility_criteria) > 0)
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Key Requirements:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                @foreach(array_slice($scholarship->eligibility_criteria, 0, 2) as $criteria)
                                    <li class="flex items-start">
                                        <svg class="w-3 h-3 mt-1 mr-2 text-green-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                        {{ $criteria }}
                                    </li>
                                @endforeach
                                @if(count($scholarship->eligibility_criteria) > 2)
                                    <li class="text-xs text-gray-500">+{{ count($scholarship->eligibility_criteria) - 2 }} more requirements</li>
                                @endif
                            </ul>
                        </div>
                    @endif
                    
                    <!-- Creator Info -->
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Posted by {{ $scholarship->creator->name }}
                    </div>

                    <!-- Actions -->
                    <div class="flex gap-2">
                        <a href="{{ route('scholarships.show', $scholarship) }}" class="flex-1 bg-green-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                            View Details
                        </a>
                        
                        @if($scholarship->application_url)
                            <a href="{{ $scholarship->application_url }}" target="_blank" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                Apply Now
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No scholarships found</h3>
                <p class="mt-1 text-sm text-gray-500">
                    @if(request()->hasAny(['search', 'min_amount', 'max_amount', 'deadline_filter']))
                        Try adjusting your search criteria.
                    @else
                        No scholarships are currently available.
                    @endif
                </p>
                @if(auth()->user()->hasManagementAccess())
                    <div class="mt-6">
                        <a href="{{ route('scholarships.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Add First Scholarship
                        </a>
                    </div>
                @endif
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($scholarships->hasPages())
        <div class="mt-8">
            {{ $scholarships->appends(request()->query())->links() }}
        </div>
    @endif
</x-unilink-layout>
