<?php

namespace App\Services;

use App\Models\Attachment;
use App\Models\Group;
use App\Models\Post;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    /**
     * Allowed file types and their MIME types
     */
    private const ALLOWED_TYPES = [
        'pdf' => ['application/pdf'],
        'doc' => ['application/msword'],
        'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'txt' => ['text/plain'],
        'rtf' => ['application/rtf', 'text/rtf'],
        'jpg' => ['image/jpeg'],
        'jpeg' => ['image/jpeg'],
        'png' => ['image/png'],
        'gif' => ['image/gif'],
        'webp' => ['image/webp'],
        'mp4' => ['video/mp4'],
        'avi' => ['video/x-msvideo'],
        'mov' => ['video/quicktime'],
        'zip' => ['application/zip'],
        'rar' => ['application/x-rar-compressed'],
        '7z' => ['application/x-7z-compressed'],
    ];

    /**
     * Upload files for a post
     */
    public function uploadPostFiles(Post $post, array $files, ?Group $group = null): array
    {
        $uploadedAttachments = [];

        foreach ($files as $file) {
            if (!$file instanceof UploadedFile || !$file->isValid()) {
                continue;
            }

            // Validate file type and size
            if (!$this->validateFile($file, $group)) {
                continue;
            }

            $attachment = $this->storeFile($file, $post);
            if ($attachment) {
                $uploadedAttachments[] = $attachment;
            }
        }

        return $uploadedAttachments;
    }

    /**
     * Validate uploaded file
     */
    private function validateFile(UploadedFile $file, ?Group $group = null): bool
    {
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();

        // Check if file type is allowed
        if (!isset(self::ALLOWED_TYPES[$extension])) {
            return false;
        }

        // Check MIME type
        if (!in_array($mimeType, self::ALLOWED_TYPES[$extension])) {
            return false;
        }

        // Check group-specific restrictions
        if ($group) {
            // Check if file sharing is enabled
            if (!$group->allow_file_sharing) {
                return false;
            }

            // Check allowed file types for the group
            if ($group->allowed_file_types && !in_array($extension, $group->allowed_file_types)) {
                return false;
            }

            // Check file size limit
            $maxSizeMB = $group->max_file_size_mb ?? 10;
            $maxSizeBytes = $maxSizeMB * 1024 * 1024;
            
            if ($file->getSize() > $maxSizeBytes) {
                return false;
            }
        } else {
            // Default size limit for non-group posts (10MB)
            if ($file->getSize() > 10 * 1024 * 1024) {
                return false;
            }
        }

        return true;
    }

    /**
     * Store file and create attachment record
     */
    private function storeFile(UploadedFile $file, Post $post): ?Attachment
    {
        try {
            $extension = strtolower($file->getClientOriginalExtension());
            $filename = Str::uuid() . '.' . $extension;
            
            // Determine storage path based on post context
            if ($post->group_id) {
                $storagePath = "groups/{$post->group_id}/attachments";
            } elseif ($post->organization_id) {
                $storagePath = "organizations/{$post->organization_id}/attachments";
            } else {
                $storagePath = "posts/attachments";
            }

            // Store the file
            $filePath = $file->storeAs($storagePath, $filename, 'public');

            // Create attachment record
            return Attachment::create([
                'post_id' => $post->id,
                'filename' => $filename,
                'original_filename' => $file->getClientOriginalName(),
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_type' => $extension,
            ]);

        } catch (\Exception $e) {
            // Log error and return null
            \Log::error('File upload failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get allowed file types for a group
     */
    public function getAllowedTypesForGroup(?Group $group = null): array
    {
        if (!$group || !$group->allowed_file_types) {
            return array_keys(self::ALLOWED_TYPES);
        }

        return $group->allowed_file_types;
    }

    /**
     * Get max file size for a group (in MB)
     */
    public function getMaxFileSizeForGroup(?Group $group = null): int
    {
        if (!$group) {
            return 10; // Default 10MB
        }

        return $group->max_file_size_mb ?? 10;
    }

    /**
     * Delete attachment and its file
     */
    public function deleteAttachment(Attachment $attachment): bool
    {
        try {
            // Delete the physical file
            if (Storage::disk('public')->exists($attachment->file_path)) {
                Storage::disk('public')->delete($attachment->file_path);
            }

            // Delete the database record
            $attachment->delete();

            return true;
        } catch (\Exception $e) {
            \Log::error('Attachment deletion failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get file type icon class
     */
    public function getFileTypeIcon(string $fileType): string
    {
        $iconMap = [
            'pdf' => 'fas fa-file-pdf text-red-500',
            'doc' => 'fas fa-file-word text-blue-500',
            'docx' => 'fas fa-file-word text-blue-500',
            'txt' => 'fas fa-file-alt text-gray-500',
            'rtf' => 'fas fa-file-alt text-gray-500',
            'jpg' => 'fas fa-file-image text-green-500',
            'jpeg' => 'fas fa-file-image text-green-500',
            'png' => 'fas fa-file-image text-green-500',
            'gif' => 'fas fa-file-image text-green-500',
            'webp' => 'fas fa-file-image text-green-500',
            'mp4' => 'fas fa-file-video text-purple-500',
            'avi' => 'fas fa-file-video text-purple-500',
            'mov' => 'fas fa-file-video text-purple-500',
            'zip' => 'fas fa-file-archive text-yellow-500',
            'rar' => 'fas fa-file-archive text-yellow-500',
            '7z' => 'fas fa-file-archive text-yellow-500',
        ];

        return $iconMap[$fileType] ?? 'fas fa-file text-gray-500';
    }
}
