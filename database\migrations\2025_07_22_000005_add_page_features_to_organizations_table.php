<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->text('about')->nullable()->after('description');
            $table->date('founded_date')->nullable()->after('about');
            $table->json('contact_info')->nullable()->after('social_links'); // Extended contact information
            $table->boolean('is_page_mode')->default(false)->after('status'); // Toggle between org and page mode
            $table->boolean('allow_public_posts')->default(false)->after('is_page_mode'); // Allow non-officers to post
            
            $table->index('is_page_mode');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropIndex(['is_page_mode']);
            $table->dropColumn(['about', 'founded_date', 'contact_info', 'is_page_mode', 'allow_public_posts']);
        });
    }
};
