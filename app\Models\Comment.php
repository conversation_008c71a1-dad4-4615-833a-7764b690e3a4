<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Comment extends Model
{
    use HasFactory;

    protected $fillable = [
        'content',
        'commentable_type',
        'commentable_id',
        'user_id',
        'parent_id',
    ];

    /**
     * Get the commentable model (post, scholarship, etc.)
     */
    public function commentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who made this comment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent comment (for replies)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    /**
     * Get all replies to this comment
     */
    public function replies(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    /**
     * Get all reactions for this comment
     */
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable');
    }

    /**
     * Get all likes for this comment (backward compatibility)
     */
    public function likes(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable')->where('type', 'like');
    }

    /**
     * Check if a user has reacted to this comment
     */
    public function isReactedBy($user): bool
    {
        if (!$user) return false;

        return $this->reactions()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if a user has liked this comment (backward compatibility)
     */
    public function isLikedBy($user): bool
    {
        if (!$user) return false;

        return $this->reactions()->where('user_id', $user->id)->where('type', 'like')->exists();
    }

    /**
     * Get user's reaction to this comment
     */
    public function getUserReaction($user)
    {
        if (!$user) return null;

        return $this->reactions()->where('user_id', $user->id)->first();
    }


}
