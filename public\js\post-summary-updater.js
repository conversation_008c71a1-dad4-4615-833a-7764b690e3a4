/**
 * Real-time Post Summary Updater
 * Handles dynamic updates for reaction summary bar, comment counts, and share counts
 */
class PostSummaryUpdater {
    constructor() {
        this.updateQueue = new Map(); // To prevent multiple simultaneous updates
        this.debounceDelay = 300; // Debounce updates to prevent spam
    }

    /**
     * Update the complete summary bar for a post
     */
    async updatePostSummary(postId) {
        // Prevent multiple simultaneous updates for the same post
        if (this.updateQueue.has(postId)) {
            return;
        }

        this.updateQueue.set(postId, true);

        try {
            const response = await fetch(`/posts/${postId}/summary`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                }
            });

            const result = await response.json();

            if (result.success) {
                this.updateSummaryBar(postId, result.data);
            }
        } catch (error) {
            console.error('Error updating post summary:', error);
        } finally {
            // Remove from queue after a delay to prevent spam
            setTimeout(() => {
                this.updateQueue.delete(postId);
            }, this.debounceDelay);
        }
    }

    /**
     * Update the summary bar elements
     */
    updateSummaryBar(postId, data) {
        const { reactions, comments, shares } = data;
        const hasAnyActivity = reactions.total > 0 || comments.total > 0 || shares.total > 0;

        // Update main post card summary bar
        const summaryBar = document.getElementById(`summary-bar-${postId}`);
        if (summaryBar) {
            summaryBar.style.display = hasAnyActivity ? 'block' : 'none';
            if (hasAnyActivity) {
                this.updateReactionSummary(postId, reactions);
                this.updateCommentSummary(postId, comments);
                this.updateShareSummary(postId, shares);
            }
        }

        // Update modal summary bar if it exists
        const modalSummaryBar = document.getElementById(`modal-summary-bar-${postId}`);
        if (modalSummaryBar) {
            modalSummaryBar.style.display = hasAnyActivity ? 'block' : 'none';
            if (hasAnyActivity) {
                this.updateModalReactionSummary(postId, reactions);
                this.updateModalCommentSummary(postId, comments);
                this.updateModalShareSummary(postId, shares);
            }
        }
    }

    /**
     * Update reaction summary (emojis and count)
     */
    updateReactionSummary(postId, reactions) {
        const reactionSummary = document.getElementById(`reaction-summary-${postId}`);
        const reactionEmojis = document.getElementById(`reaction-emojis-${postId}`);
        const reactionCount = document.getElementById(`reaction-count-${postId}`);

        if (!reactionSummary || !reactionEmojis || !reactionCount) return;

        if (reactions.total > 0) {
            // Show reaction summary
            reactionSummary.style.display = 'flex';
            
            // Update count
            reactionCount.textContent = reactions.total;
            
            // Update emojis
            reactionEmojis.innerHTML = '';
            reactions.top_reactions.forEach(reaction => {
                const emojiDiv = document.createElement('div');
                emojiDiv.className = 'w-5 h-5 rounded-full bg-white border border-gray-200 flex items-center justify-center';
                emojiDiv.innerHTML = `<img src="${reaction.emoji}" alt="${reaction.label}" class="w-4 h-4">`;
                reactionEmojis.appendChild(emojiDiv);
            });
        } else {
            // Hide reaction summary
            reactionSummary.style.display = 'none';
        }
    }

    /**
     * Update comment summary
     */
    updateCommentSummary(postId, comments) {
        const commentSummary = document.getElementById(`comment-summary-${postId}`);
        if (!commentSummary) return;

        if (comments.total > 0) {
            commentSummary.style.display = 'inline';
            commentSummary.textContent = `${comments.total} comment${comments.total !== 1 ? 's' : ''}`;
        } else {
            commentSummary.style.display = 'none';
        }
    }

    /**
     * Update share summary
     */
    updateShareSummary(postId, shares) {
        const shareSummary = document.getElementById(`share-summary-${postId}`);
        if (!shareSummary) return;

        if (shares.total > 0) {
            shareSummary.style.display = 'inline';
            shareSummary.textContent = `${shares.total} share${shares.total !== 1 ? 's' : ''}`;
        } else {
            shareSummary.style.display = 'none';
        }
    }

    /**
     * Update modal reaction summary (emojis and count)
     */
    updateModalReactionSummary(postId, reactions) {
        const reactionSummary = document.getElementById(`modal-reaction-summary-${postId}`);
        const reactionEmojis = document.getElementById(`modal-reaction-emojis-${postId}`);
        const reactionCount = document.getElementById(`modal-reaction-count-${postId}`);

        if (!reactionSummary || !reactionEmojis || !reactionCount) return;

        if (reactions.total > 0) {
            // Show reaction summary
            reactionSummary.style.display = 'flex';

            // Update count
            reactionCount.textContent = reactions.total;

            // Update emojis
            reactionEmojis.innerHTML = '';
            reactions.top_reactions.forEach(reaction => {
                const emojiDiv = document.createElement('div');
                emojiDiv.className = 'w-5 h-5 rounded-full bg-white border border-gray-200 flex items-center justify-center';
                emojiDiv.innerHTML = `<img src="${reaction.emoji}" alt="${reaction.label}" class="w-4 h-4">`;
                reactionEmojis.appendChild(emojiDiv);
            });
        } else {
            // Hide reaction summary
            reactionSummary.style.display = 'none';
        }
    }

    /**
     * Update modal comment summary
     */
    updateModalCommentSummary(postId, comments) {
        const commentSummary = document.getElementById(`modal-comment-summary-${postId}`);
        if (!commentSummary) return;

        if (comments.total > 0) {
            commentSummary.style.display = 'inline';
            commentSummary.textContent = `${comments.total} comment${comments.total !== 1 ? 's' : ''}`;
        } else {
            commentSummary.style.display = 'none';
        }
    }

    /**
     * Update modal share summary
     */
    updateModalShareSummary(postId, shares) {
        const shareSummary = document.getElementById(`modal-share-summary-${postId}`);
        if (!shareSummary) return;

        if (shares.total > 0) {
            shareSummary.style.display = 'inline';
            shareSummary.textContent = `${shares.total} share${shares.total !== 1 ? 's' : ''}`;
        } else {
            shareSummary.style.display = 'none';
        }
    }

    /**
     * Trigger update after a reaction change
     */
    onReactionChange(postId) {
        // Debounced update
        setTimeout(() => {
            this.updatePostSummary(postId);
        }, 100);
    }

    /**
     * Trigger update after a comment change
     */
    onCommentChange(postId) {
        // Debounced update
        setTimeout(() => {
            this.updatePostSummary(postId);
        }, 100);
    }

    /**
     * Trigger update after a share change
     */
    onShareChange(postId) {
        // Debounced update
        setTimeout(() => {
            this.updatePostSummary(postId);
        }, 100);
    }
}

// Initialize the updater only if it doesn't exist
if (!window.postSummaryUpdater) {
    window.postSummaryUpdater = new PostSummaryUpdater();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PostSummaryUpdater;
}
