<x-unilink-layout>
    <!-- Organization Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            @if($organization->cover_image)
                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
            @endif
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                @auth
                    @if($userMembership)
                        @if($userMembership->pivot->status === 'pending')
                            <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                                Pending Approval
                            </span>
                        @elseif($userMembership->pivot->status === 'active')
                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                {{ ucfirst($userMembership->pivot->role) }}
                            </span>
                            @if($userMembership->pivot->role !== 'president')
                                <form action="{{ route('organizations.leave', $organization) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to leave this organization?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium hover:bg-red-700">
                                        Leave
                                    </button>
                                </form>
                            @endif
                        @endif
                    @else
                        <form action="{{ route('organizations.join', $organization) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700">
                                Join Organization
                            </button>
                        </form>
                    @endif
                    
                    @if(auth()->user()->isAdmin() || $organization->created_by === auth()->id() || ($userMembership && in_array($userMembership->pivot->role, ['officer', 'president'])))
                        <a href="{{ route('organizations.edit', $organization) }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                            Edit
                        </a>
                    @endif
                @endauth
            </div>
        </div>

        <!-- Organization Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-10 border-4 border-white">
                        @if($organization->logo)
                            <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-16 h-16 rounded-lg object-cover">
                        @else
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg">{{ substr($organization->name, 0, 2) }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Organization Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $organization->name }}</h1>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $organization->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                            {{ ucfirst($organization->status) }}
                        </span>
                    </div>
                    
                    <p class="text-gray-600 mt-2">{{ $organization->description }}</p>
                    
                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {{ $organization->activeMembers->count() }} members
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Created by {{ $organization->creator->name }}
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                            </svg>
                            {{ $organization->created_at->format('M Y') }}
                        </div>
                    </div>

                    <!-- Contact Info -->
                    @if($organization->email || $organization->phone || $organization->website)
                        <div class="flex items-center space-x-4 mt-4">
                            @if($organization->email)
                                <a href="mailto:{{ $organization->email }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    {{ $organization->email }}
                                </a>
                            @endif
                            @if($organization->phone)
                                <a href="tel:{{ $organization->phone }}" class="text-blue-600 hover:text-blue-800 text-sm">
                                    {{ $organization->phone }}
                                </a>
                            @endif
                            @if($organization->website)
                                <a href="{{ $organization->website }}" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                                    Website
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Recent Posts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Posts</h2>
                </div>
                
                @if($organization->posts->count() > 0)
                    <div class="space-y-6">
                        @foreach($organization->posts as $post)
                            <x-post-card :post="$post" />
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This organization hasn't posted anything yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Members ({{ $organization->activeMembers->count() }})</h3>
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        @foreach($organization->activeMembers->take(10) as $member)
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="{{ $member->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ $member->name }}">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $member->name }}</p>
                                    <p class="text-xs text-gray-500">{{ ucfirst($member->pivot->role) }}</p>
                                </div>
                                @if($member->pivot->role === 'president')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        President
                                    </span>
                                @elseif($member->pivot->role === 'officer')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Officer
                                    </span>
                                @endif
                            </div>
                        @endforeach
                        
                        @if($organization->activeMembers->count() > 10)
                            <div class="text-center pt-2">
                                <button class="text-sm text-blue-600 hover:text-blue-800">
                                    View all {{ $organization->activeMembers->count() }} members
                                </button>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-unilink-layout>
