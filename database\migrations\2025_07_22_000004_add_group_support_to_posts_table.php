<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->foreignId('group_id')->nullable()->after('organization_id')->constrained()->onDelete('cascade');
            $table->json('attachments')->nullable()->after('images'); // For file attachments
            $table->enum('approval_status', ['approved', 'pending', 'rejected'])->default('approved')->after('status');
            $table->timestamp('approved_at')->nullable()->after('approval_status');
            $table->foreignId('approved_by')->nullable()->after('approved_at')->constrained('users')->onDelete('set null');
            
            // Add indexes
            $table->index('group_id');
            $table->index(['approval_status', 'group_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropForeign(['approved_by']);
            $table->dropForeign(['group_id']);
            $table->dropIndex(['approval_status', 'group_id']);
            $table->dropIndex(['group_id']);
            $table->dropColumn(['group_id', 'attachments', 'approval_status', 'approved_at', 'approved_by']);
        });
    }
};
