<x-unilink-layout>
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h1 class="text-2xl font-bold text-gray-900">Create New Group</h1>
                <p class="text-gray-600 mt-1">Create a space for students to connect and collaborate</p>
            </div>

            <form action="{{ route('groups.store') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                    
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Group Name *</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               placeholder="Enter group name">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea name="description" id="description" rows="3"
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                  placeholder="Describe what this group is about...">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="organization_id" class="block text-sm font-medium text-gray-700 mb-1">Associated Organization (Optional)</label>
                        <select name="organization_id" id="organization_id" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">No organization</option>
                            @foreach($organizations as $org)
                                <option value="{{ $org->id }}" {{ old('organization_id') == $org->id ? 'selected' : '' }}>
                                    {{ $org->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('organization_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Privacy & Access</h3>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Group Visibility *</label>
                        <div class="space-y-2">
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="visibility" value="public" {{ old('visibility', 'public') === 'public' ? 'checked' : '' }}
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Public</div>
                                    <div class="text-sm text-gray-500">Anyone can see the group and join immediately</div>
                                </div>
                            </label>
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="visibility" value="private" {{ old('visibility') === 'private' ? 'checked' : '' }}
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Private</div>
                                    <div class="text-sm text-gray-500">Only members can see the group, join requests need approval</div>
                                </div>
                            </label>
                        </div>
                        @error('visibility')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Post Approval *</label>
                        <div class="space-y-2">
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="post_approval" value="none" {{ old('post_approval', 'none') === 'none' ? 'checked' : '' }}
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">No Approval Required</div>
                                    <div class="text-sm text-gray-500">Members can post immediately</div>
                                </div>
                            </label>
                            <label class="flex items-start space-x-3">
                                <input type="radio" name="post_approval" value="required" {{ old('post_approval') === 'required' ? 'checked' : '' }}
                                       class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Approval Required</div>
                                    <div class="text-sm text-gray-500">Posts need moderator approval before being visible</div>
                                </div>
                            </label>
                        </div>
                        @error('post_approval')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- File Sharing Settings -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">File Sharing</h3>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="allow_file_sharing" id="allow_file_sharing" value="1" 
                               {{ old('allow_file_sharing', true) ? 'checked' : '' }}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="allow_file_sharing" class="ml-2 text-sm text-gray-900">
                            Allow members to share files
                        </label>
                    </div>

                    <div id="file-sharing-options" class="space-y-4 {{ old('allow_file_sharing', true) ? '' : 'hidden' }}">
                        <div>
                            <label for="max_file_size_mb" class="block text-sm font-medium text-gray-700 mb-1">Maximum File Size (MB)</label>
                            <input type="number" name="max_file_size_mb" id="max_file_size_mb" 
                                   value="{{ old('max_file_size_mb', 10) }}" min="1" max="100"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            @error('max_file_size_mb')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Allowed File Types</label>
                            <div class="grid grid-cols-2 gap-2">
                                @php
                                    $fileTypes = [
                                        'pdf' => 'PDF Documents',
                                        'doc' => 'Word Documents',
                                        'docx' => 'Word Documents (New)',
                                        'txt' => 'Text Files',
                                        'jpg' => 'JPEG Images',
                                        'png' => 'PNG Images',
                                        'gif' => 'GIF Images',
                                        'zip' => 'ZIP Archives'
                                    ];
                                    $oldTypes = old('allowed_file_types', ['pdf', 'doc', 'docx', 'jpg', 'png']);
                                @endphp
                                @foreach($fileTypes as $type => $label)
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" name="allowed_file_types[]" value="{{ $type }}"
                                               {{ in_array($type, $oldTypes) ? 'checked' : '' }}
                                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                        <span class="text-sm text-gray-700">{{ $label }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('allowed_file_types')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Images -->
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Group Images</h3>
                    
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700 mb-1">Group Logo</label>
                        <input type="file" name="logo" id="logo" accept="image/*"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-500">Recommended size: 200x200px. Max size: 2MB.</p>
                        @error('logo')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="cover_image" class="block text-sm font-medium text-gray-700 mb-1">Cover Image</label>
                        <input type="file" name="cover_image" id="cover_image" accept="image/*"
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <p class="mt-1 text-sm text-gray-500">Recommended size: 1200x400px. Max size: 5MB.</p>
                        @error('cover_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('groups.index') }}" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700">
                        Create Group
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Toggle file sharing options
        document.getElementById('allow_file_sharing').addEventListener('change', function() {
            const options = document.getElementById('file-sharing-options');
            if (this.checked) {
                options.classList.remove('hidden');
            } else {
                options.classList.add('hidden');
            }
        });
    </script>
</x-unilink-layout>
