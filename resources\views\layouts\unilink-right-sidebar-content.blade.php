<div class="p-4 space-y-4">
    @auth
    <!-- My Groups & Organizations -->
    <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
        <h3 class="text-lg font-bold text-custom-lightest mb-4 bg-custom-green text-custom-darkest px-3 py-2 rounded-t-lg -mx-4 -mt-4 mb-4">My Communities</h3>
        <div class="space-y-3 text-sm">
            <div>
                <a href="{{ route('groups.my') }}" class="flex items-center justify-between text-custom-green hover:text-custom-second-darkest hover:underline">
                    <span>My Groups</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </a>
            </div>
            <div>
                <a href="{{ route('organizations.my') }}" class="flex items-center justify-between text-custom-green hover:text-custom-second-darkest hover:underline">
                    <span>My Organizations</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </a>
            </div>
            <div class="border-t border-custom-second-darkest border-opacity-20 pt-2">
                <a href="{{ route('groups.create') }}" class="flex items-center justify-between text-blue-600 hover:text-blue-800 hover:underline">
                    <span>Create Group</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
    @endauth

    <!-- Upcoming Events -->
    <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
        <h3 class="text-lg font-bold text-custom-lightest mb-4 bg-custom-green text-custom-darkest px-3 py-2 rounded-t-lg -mx-4 -mt-4 mb-4">Upcoming Events</h3>
        <div class="space-y-3 text-sm">
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">Enrollment Period: Aug 1-15</p>
            </div>
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">Orientation Day: Aug 20</p>
            </div>
            <div class="border-b border-custom-second-darkest border-opacity-20 pb-2">
                <p class="font-medium text-custom-darkest">First Day of Classes: Aug 22</p>
            </div>
            <div>
                <p class="font-medium text-custom-darkest">Foundation Day: Sept 15</p>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="bg-white rounded-lg p-4 border border-custom-second-darkest border-opacity-20">
        <h3 class="text-lg font-bold text-custom-lightest mb-4 bg-custom-green text-custom-darkest px-3 py-2 rounded-t-lg -mx-4 -mt-4 mb-4">Quick Links</h3>
        <div class="space-y-2 text-sm">
            <a href="#" class="block text-custom-green hover:text-custom-second-darkest hover:underline">University Website</a>
            <a href="#" class="block text-custom-green hover:text-custom-second-darkest hover:underline">Student Portal</a>
            <a href="#" class="block text-custom-green hover:text-custom-second-darkest hover:underline">Library Resources</a>
            <a href="#" class="block text-custom-green hover:text-custom-second-darkest hover:underline">Academic Calendar</a>
        </div>
    </div>
</div>
