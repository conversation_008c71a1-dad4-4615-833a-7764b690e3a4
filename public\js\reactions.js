// Facebook-style Reaction System JavaScript

class FacebookReactionSystem {
    constructor() {
        this.hideTimeout = null;
        this.longPressTimer = null;
        this.longPressDelay = 500; // 500ms for long press
        this.isLongPress = false;
        this.reactions = [
            { type: 'like', emoji: '👍', label: 'Like', color: 'text-blue-600' },
            { type: 'love', emoji: '❤️', label: 'Love', color: 'text-red-600' },
            { type: 'haha', emoji: '😂', label: 'Haha', color: 'text-yellow-600' },
            { type: 'wow', emoji: '😮', label: 'Wow', color: 'text-orange-600' },
            { type: 'sad', emoji: '😢', label: 'Sad', color: 'text-yellow-600' },
            { type: 'angry', emoji: '😡', label: 'Angry', color: 'text-red-600' }
        ];
        this.init();
    }

    init() {
        console.log('Facebook Reaction system initialized');
        this.bindEvents();
        this.createReactionPopups();
    }

    bindEvents() {
        // Handle clicks outside reaction popups to close them
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.reaction-wrapper')) {
                this.hideAllPopups();
            }
        });

        // Delegate event listeners for reaction buttons
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleReactionButtonHover(e.target.closest('.reaction-btn'));
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.reaction-wrapper')) {
                this.handleReactionWrapperLeave(e.target.closest('.reaction-wrapper'));
            }
        }, true);

        document.addEventListener('click', (e) => {
            if (e.target.closest('.reaction-btn')) {
                e.preventDefault();
                this.handleReactionButtonClick(e.target.closest('.reaction-btn'));
            } else if (e.target.closest('.reaction-option')) {
                e.preventDefault();
                this.handleReactionOptionClick(e.target.closest('.reaction-option'));
            }
        });

        // Mobile long press support
        document.addEventListener('touchstart', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchStart(e, e.target.closest('.reaction-btn'));
            }
        });

        document.addEventListener('touchend', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchEnd(e, e.target.closest('.reaction-btn'));
            }
        });
    }

    createReactionPopups() {
        // Find all reaction wrappers and create popups for them
        const wrappers = document.querySelectorAll('.reaction-wrapper');
        wrappers.forEach(wrapper => {
            if (!wrapper.querySelector('.reaction-popup')) {
                this.createPopupForWrapper(wrapper);
            }
        });
    }

    createPopupForWrapper(wrapper) {
        const popup = document.createElement('div');
        popup.className = 'reaction-popup absolute bottom-full left-0 mb-2 bg-white rounded-full shadow-lg border flex items-center px-2 py-1 space-x-1 opacity-0 invisible transform scale-95 transition-all duration-200 z-50';

        this.reactions.forEach(reaction => {
            const option = document.createElement('button');
            option.className = 'reaction-option flex flex-col items-center p-2 rounded-lg hover:bg-gray-100 transition-all duration-200 transform hover:scale-110';
            option.dataset.reactionType = reaction.type;
            option.dataset.targetId = wrapper.querySelector('.reaction-btn').dataset.targetId;
            option.dataset.targetType = wrapper.querySelector('.reaction-btn').dataset.targetType;

            option.innerHTML = `
                <span class="text-2xl mb-1">${reaction.emoji}</span>
                <span class="text-xs font-medium text-gray-600">${reaction.label}</span>
            `;

            popup.appendChild(option);
        });

        wrapper.appendChild(popup);
    }

    handleReactionButtonHover(button) {
        const wrapper = button.closest('.reaction-wrapper');
        const popup = wrapper.querySelector('.reaction-popup');

        clearTimeout(this.hideTimeout);

        // Show popup after a short delay
        setTimeout(() => {
            if (wrapper.matches(':hover')) {
                this.showPopup(popup);
            }
        }, 300);
    }

    handleReactionWrapperLeave(wrapper) {
        const popup = wrapper.querySelector('.reaction-popup');
        this.hidePopupWithDelay(popup);
    }

    handleReactionButtonClick(button) {
        if (this.isLongPress) {
            this.isLongPress = false;
            return;
        }

        // Quick click - toggle like
        this.toggleDefaultReaction(button);
    }

    handleReactionOptionClick(option) {
        this.selectReaction(option);
    }

    handleTouchStart(e, button) {
        this.isLongPress = false;
        this.longPressTimer = setTimeout(() => {
            this.isLongPress = true;
            const wrapper = button.closest('.reaction-wrapper');
            const popup = wrapper.querySelector('.reaction-popup');
            this.showPopup(popup);

            // Haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }, this.longPressDelay);
    }

    handleTouchEnd(e, button) {
        clearTimeout(this.longPressTimer);

        if (!this.isLongPress) {
            // Short tap - toggle like
            setTimeout(() => {
                if (!this.isLongPress) {
                    this.toggleDefaultReaction(button);
                }
            }, 50);
        }
    }

    showPopup(popup) {
        if (!popup) return;

        clearTimeout(this.hideTimeout);
        this.hideAllPopups();

        popup.classList.remove('opacity-0', 'invisible', 'scale-95');
        popup.classList.add('opacity-100', 'visible', 'scale-100');
    }

    hidePopupWithDelay(popup) {
        if (!popup) return;

        this.hideTimeout = setTimeout(() => {
            this.hidePopup(popup);
        }, 300);
    }

    hidePopup(popup) {
        if (!popup) return;

        popup.classList.remove('opacity-100', 'visible', 'scale-100');
        popup.classList.add('opacity-0', 'invisible', 'scale-95');
    }

    hideAllPopups() {
        const popups = document.querySelectorAll('.reaction-popup');
        popups.forEach(popup => this.hidePopup(popup));
    }

    async toggleDefaultReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;

        console.log('Toggle default reaction:', { targetId, targetType, currentReaction });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: currentReaction ? null : 'like' // Toggle off if exists, otherwise set to like
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateReactionButton(button, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    }

    async selectReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const reactionType = button.dataset.reactionType;

        console.log('Select reaction:', { targetId, targetType, reactionType });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: reactionType
                })
            });

            const data = await response.json();

            if (data.success) {
                // Find the main reaction button
                const mainButton = button.closest('.reaction-wrapper').querySelector('.reaction-btn');
                this.updateReactionButton(mainButton, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);

                // Hide the popup
                const popup = button.closest('.reaction-popup');
                this.hidePopup(popup);
            }
        } catch (error) {
            console.error('Error selecting reaction:', error);
        }
    }

    updateReactionButton(button, reaction) {
        if (!button) return;

        const wrapper = button.closest('.reaction-wrapper');
        if (!wrapper) return;

        if (reaction) {
            // Update button to show the reaction
            button.innerHTML = `
                <span class="text-lg">${reaction.emoji}</span>
                <span class="text-sm font-medium">${reaction.label}</span>
            `;
            button.className = `reaction-btn flex items-center space-x-2 ${reaction.color} transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100`;
            button.dataset.currentReaction = reaction.type;
        } else {
            // Reset button to default state
            button.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                </svg>
                <span class="text-sm font-medium">Like</span>
            `;
            button.className = 'reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100';
            button.dataset.currentReaction = '';
        }
    }

    updateReactionCounts(targetId, targetType, counts) {
        // Update reaction counts in the UI
        const countElement = document.querySelector(`[data-target-id="${targetId}"][data-target-type="${targetType}"] .reaction-count`);
        if (countElement && counts.total > 0) {
            countElement.textContent = counts.total;
            countElement.style.display = 'inline';
        } else if (countElement) {
            countElement.style.display = 'none';
        }

        console.log('Update reaction counts:', { targetId, targetType, counts });
    }
}

// Initialize the reaction system when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.facebookReactionSystem = new FacebookReactionSystem();
});

// Make it globally available
window.FacebookReactionSystem = FacebookReactionSystem;