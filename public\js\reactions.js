// Facebook-style Reaction System JavaScript

class FacebookReactionSystem {
    constructor() {
        this.hideTimeout = null;
        this.longPressTimer = null;
        this.longPressDelay = 500; // 500ms for long press
        this.isLongPress = false;
        this.reactions = [
            { type: 'like', emoji: '/assets/emojis/like.gif', label: 'Like', color: 'text-blue-600' },
            { type: 'love', emoji: '/assets/emojis/love.gif', label: 'Love', color: 'text-red-600' },
            { type: 'haha', emoji: '/assets/emojis/haha.gif', label: 'Haha', color: 'text-yellow-600' },
            { type: 'wow', emoji: '/assets/emojis/wow.gif', label: 'Wow', color: 'text-orange-600' },
            { type: 'sad', emoji: '/assets/emojis/sad.gif', label: 'Sad', color: 'text-yellow-600' },
            { type: 'angry', emoji: '/assets/emojis/angry.gif', label: 'Angry', color: 'text-red-600' }
        ];
        this.init();
    }

    init() {
        console.log('Facebook Reaction system initialized');
        this.bindEvents();
        this.createReactionPopups();
    }

    bindEvents() {
        // Handle clicks outside reaction popups to close them
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.reaction-wrapper')) {
                this.hideAllPopups();
            }
        });

        // Delegate event listeners for reaction buttons and popups
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleReactionButtonHover(e.target.closest('.reaction-btn'));
            } else if (e.target.closest('.reaction-popup')) {
                this.handlePopupHover(e.target.closest('.reaction-popup'));
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.reaction-wrapper')) {
                this.handleReactionWrapperLeave(e.target.closest('.reaction-wrapper'));
            }
        }, true);

        document.addEventListener('click', (e) => {
            if (e.target.closest('.reaction-btn')) {
                e.preventDefault();
                this.handleReactionButtonClick(e.target.closest('.reaction-btn'));
            } else if (e.target.closest('.reaction-option')) {
                e.preventDefault();
                this.handleReactionOptionClick(e.target.closest('.reaction-option'));
            }
        });

        // Mobile long press support
        document.addEventListener('touchstart', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchStart(e, e.target.closest('.reaction-btn'));
            }
        });

        document.addEventListener('touchend', (e) => {
            if (e.target.closest('.reaction-btn')) {
                this.handleTouchEnd(e, e.target.closest('.reaction-btn'));
            }
        });
    }

    createReactionPopups() {
        // Find all reaction wrappers and create popups for them
        const wrappers = document.querySelectorAll('.reaction-wrapper');
        wrappers.forEach(wrapper => {
            if (!wrapper.querySelector('.reaction-popup')) {
                this.createPopupForWrapper(wrapper);
            }
        });
    }

    createPopupForWrapper(wrapper) {
        const popup = document.createElement('div');
        popup.className = 'reaction-popup absolute bottom-full left-0 mb-2 bg-white rounded-full shadow-lg border flex items-center opacity-0 invisible transform scale-95 transition-all duration-200 z-[9999]';

        this.reactions.forEach(reaction => {
            const option = document.createElement('button');
            option.className = 'reaction-option flex flex-col items-center p-2 rounded-full transition-all duration-200 transform hover:scale-110';
            option.dataset.reactionType = reaction.type;
            option.dataset.targetId = wrapper.querySelector('.reaction-btn').dataset.targetId;
            option.dataset.targetType = wrapper.querySelector('.reaction-btn').dataset.targetType;

            option.innerHTML = `
                <img src="${reaction.emoji}" alt="${reaction.label}" class="reaction-emoji"
                     onerror="this.style.display='none';">
            `;

            popup.appendChild(option);
        });

        wrapper.appendChild(popup);
    }

    handleReactionButtonHover(button) {
        const wrapper = button.closest('.reaction-wrapper');
        const popup = wrapper.querySelector('.reaction-popup');

        clearTimeout(this.hideTimeout);

        // Show popup after a short delay
        this.showTimeout = setTimeout(() => {
            if (wrapper.matches(':hover')) {
                this.showPopup(popup);
            }
        }, 300);
    }

    handlePopupHover(popup) {
        // Keep popup visible when hovering over it
        clearTimeout(this.hideTimeout);
    }

    handleReactionWrapperLeave(wrapper) {
        const popup = wrapper.querySelector('.reaction-popup');

        // Add a small delay to allow moving from button to popup
        setTimeout(() => {
            // Check if mouse is still over the wrapper or popup
            if (!wrapper.matches(':hover') && !popup.matches(':hover')) {
                this.hidePopupWithDelay(popup);
            }
        }, 100);
    }

    handleReactionButtonClick(button) {
        if (this.isLongPress) {
            this.isLongPress = false;
            return;
        }

        // Quick click - toggle like
        this.toggleDefaultReaction(button);
    }

    handleReactionOptionClick(option) {
        this.selectReaction(option);
    }

    handleTouchStart(e, button) {
        this.isLongPress = false;
        this.longPressTimer = setTimeout(() => {
            this.isLongPress = true;
            const wrapper = button.closest('.reaction-wrapper');
            const popup = wrapper.querySelector('.reaction-popup');
            this.showPopup(popup);

            // Haptic feedback if available
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }, this.longPressDelay);
    }

    handleTouchEnd(e, button) {
        clearTimeout(this.longPressTimer);

        if (!this.isLongPress) {
            // Short tap - toggle like
            setTimeout(() => {
                if (!this.isLongPress) {
                    this.toggleDefaultReaction(button);
                }
            }, 50);
        }
    }

    showPopup(popup) {
        if (!popup) return;

        clearTimeout(this.hideTimeout);
        this.hideAllPopups();

        // Smart positioning to avoid viewport cutoff
        this.adjustPopupPosition(popup);

        popup.classList.remove('opacity-0', 'invisible', 'scale-95');
        popup.classList.add('opacity-100', 'visible', 'scale-100');
    }

    adjustPopupPosition(popup) {
        if (!popup) return;

        const wrapper = popup.closest('.reaction-wrapper');
        if (!wrapper) return;

        const wrapperRect = wrapper.getBoundingClientRect();
        const popupHeight = 80; // Approximate popup height
        const viewportHeight = window.innerHeight;

        // Check if popup would be cut off at the top
        if (wrapperRect.top - popupHeight < 20) {
            // Position below the button instead
            popup.classList.add('position-top');
            popup.style.bottom = 'auto';
            popup.style.top = '100%';
            popup.style.marginBottom = '0';
            popup.style.marginTop = '0.5rem';
        } else {
            // Position above the button (default)
            popup.classList.remove('position-top');
            popup.style.bottom = '100%';
            popup.style.top = 'auto';
            popup.style.marginBottom = '0.5rem';
            popup.style.marginTop = '0';
        }
    }

    hidePopupWithDelay(popup) {
        if (!popup) return;

        this.hideTimeout = setTimeout(() => {
            // Double-check that mouse is not over wrapper or popup before hiding
            const wrapper = popup.closest('.reaction-wrapper');
            if (wrapper && !wrapper.matches(':hover') && !popup.matches(':hover')) {
                this.hidePopup(popup);
            }
        }, 300);
    }

    hidePopup(popup) {
        if (!popup) return;

        popup.classList.remove('opacity-100', 'visible', 'scale-100');
        popup.classList.add('opacity-0', 'invisible', 'scale-95');
    }

    hideAllPopups() {
        const popups = document.querySelectorAll('.reaction-popup');
        popups.forEach(popup => this.hidePopup(popup));
    }

    async toggleDefaultReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const currentReaction = button.dataset.currentReaction;

        console.log('Toggle default reaction:', { targetId, targetType, currentReaction });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: currentReaction ? null : 'like' // Toggle off if exists, otherwise set to like
                })
            });

            const data = await response.json();

            if (data.success) {
                this.updateReactionButton(button, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    }

    async selectReaction(button) {
        const targetId = button.dataset.targetId;
        const targetType = button.dataset.targetType;
        const reactionType = button.dataset.reactionType;

        console.log('Select reaction:', { targetId, targetType, reactionType });

        try {
            const response = await fetch(`/reactions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    target_id: targetId,
                    target_type: targetType,
                    reaction_type: reactionType
                })
            });

            const data = await response.json();

            if (data.success) {
                // Find the main reaction button
                const mainButton = button.closest('.reaction-wrapper').querySelector('.reaction-btn');
                this.updateReactionButton(mainButton, data.reaction);
                this.updateReactionCounts(targetId, targetType, data.counts);

                // Hide the popup
                const popup = button.closest('.reaction-popup');
                this.hidePopup(popup);
            }
        } catch (error) {
            console.error('Error selecting reaction:', error);
        }
    }

    updateReactionButton(button, reaction) {
        if (!button) return;

        const wrapper = button.closest('.reaction-wrapper');
        if (!wrapper) return;

        if (reaction) {
            // Update button to show the reaction
            button.innerHTML = `
                <img src="${reaction.emoji}" alt="${reaction.label}" class="w-5 h-5 reaction-emoji"
                     onerror="this.style.display='none';">
                <span class="text-sm font-medium">${reaction.label}</span>
            `;
            button.className = `reaction-btn flex items-center space-x-2 ${reaction.color} transition-colors duration-200 py-2 px-3 rounded-lg `;
            button.dataset.currentReaction = reaction.type;
        } else {
            // Reset button to default state
            button.innerHTML = `
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
                </svg>
                <span class="text-sm font-medium">Like</span>
            `;
            button.className = 'reaction-btn flex items-center space-x-2 text-gray-500 transition-colors duration-200 py-2 px-3 rounded-lg ';
            button.dataset.currentReaction = '';
        }
    }

    updateReactionCounts(targetId, targetType, counts) {
        console.log('Update reaction counts called:', { targetId, targetType, counts });

        // Find the post card containing this reaction
        const postCard = document.querySelector(`[data-post-id="${targetId}"]`);
        if (!postCard) {
            console.warn('Post card not found for targetId:', targetId);
            return;
        }

        console.log('Found post card, updating summary bar...');

        // Update the reaction summary bar
        this.updateReactionSummaryBar(postCard, counts);

        // Update any legacy count displays
        const countElements = document.querySelectorAll(`[data-target-id="${targetId}"] .reaction-count`);
        countElements.forEach(element => {
            if (counts.total > 0) {
                element.textContent = counts.total;
                element.style.display = 'inline';
            } else {
                element.style.display = 'none';
            }
        });
    }

    updateReactionSummaryBar(postCard, counts) {
        console.log('updateReactionSummaryBar called with counts:', counts);

        // Find or create the summary bar
        let summaryBar = postCard.querySelector('.reaction-summary-bar');
        console.log('Existing summary bar found:', !!summaryBar);

        // Find the actions section more reliably - try multiple selectors
        let actionsSection = postCard.querySelector('div[class*="bg-gray-50"]');
        if (!actionsSection) {
            actionsSection = postCard.querySelector('.post-actions');
        }
        if (!actionsSection) {
            // Look for any div that contains reaction buttons
            const reactionWrapper = postCard.querySelector('.reaction-wrapper');
            if (reactionWrapper) {
                actionsSection = reactionWrapper.closest('div[class*="px-4"]');
            }
        }
        if (!actionsSection) {
            // Last resort: find any div with padding classes
            actionsSection = postCard.querySelector('div[class*="py-3"][class*="px-4"]');
        }

        const postId = postCard.dataset.postId;
        console.log('Post ID:', postId, 'Actions section found:', !!actionsSection);

        // Debug logging
        if (!actionsSection) {
            console.warn('Could not find actions section for post', postId);
            console.log('Post card structure:', postCard.innerHTML.substring(0, 500));
        }

        // Get current comments and shares counts from existing elements
        const commentsElement = document.getElementById(`comments-summary-${postId}`);
        const sharesElement = document.getElementById(`shares-summary-${postId}`);
        const commentsCount = commentsElement ? this.extractCount(commentsElement.textContent) : 0;
        const sharesCount = sharesElement ? this.extractCount(sharesElement.textContent) : 0;

        // Check if we should show the summary bar
        const shouldShow = counts.total > 0 || commentsCount > 0 || sharesCount > 0;

        if (!shouldShow) {
            // Remove summary bar if no reactions, comments, or shares
            if (summaryBar) {
                summaryBar.remove();
            }
            return;
        }

        // Create summary bar if it doesn't exist
        if (!summaryBar) {
            summaryBar = document.createElement('div');
            summaryBar.className = 'reaction-summary-bar px-4 py-3 border-t border-gray-200';
            summaryBar.dataset.postId = postId;

            // Insert the summary bar before the actions section
            if (actionsSection && actionsSection.parentNode) {
                actionsSection.parentNode.insertBefore(summaryBar, actionsSection);
            } else {
                // Fallback: append to the post card if actions section not found
                console.warn('Actions section not found, appending summary bar to post card');
                postCard.appendChild(summaryBar);
            }
        }

        // Build the summary bar content
        const emojisHtml = this.buildReactionEmojisHtml(counts.by_type);
        const totalCount = counts.total;

        console.log('Building summary bar content:', {
            totalCount,
            byType: counts.by_type,
            emojisHtml,
            commentsCount,
            sharesCount
        });

        // Build right side content (comments and shares)
        let rightSideHtml = '';
        if (commentsCount > 0) {
            rightSideHtml += `<span class="hover:underline cursor-pointer" onclick="openCommentModal(${postId})" id="comments-summary-${postId}">
                ${commentsCount} comment${commentsCount !== 1 ? 's' : ''}
            </span>`;
        }
        if (sharesCount > 0) {
            rightSideHtml += `<span id="shares-summary-${postId}">${sharesCount} share${sharesCount !== 1 ? 's' : ''}</span>`;
        }

        const newHtml = `
            <div class="flex items-center justify-between text-sm text-gray-600">
                <!-- Left: Reaction emojis and count -->
                <div class="flex items-center space-x-2">
                    ${totalCount > 0 ? `
                        <div class="flex items-center space-x-1">
                            <!-- Show top reaction emojis -->
                            <div class="flex -space-x-1">
                                ${emojisHtml}
                            </div>
                            <span class="font-medium">${totalCount}</span>
                        </div>
                    ` : ''}
                </div>

                <!-- Right: Comments and shares -->
                <div class="flex items-center space-x-4">
                    ${rightSideHtml}
                </div>
            </div>
        `;

        console.log('Setting summary bar innerHTML to:', newHtml);
        summaryBar.innerHTML = newHtml;
        console.log('Summary bar updated successfully');
    }

    extractCount(text) {
        const match = text.match(/(\d+)/);
        return match ? parseInt(match[1]) : 0;
    }

    buildReactionEmojisHtml(reactionsByType) {
        console.log('buildReactionEmojisHtml called with:', reactionsByType);

        if (!reactionsByType || Object.keys(reactionsByType).length === 0) {
            console.log('No reactions by type, returning empty string');
            return '';
        }

        // Sort reactions by count (descending) and take top 3
        const sortedReactions = Object.entries(reactionsByType)
            .sort(([,a], [,b]) => b.count - a.count)
            .slice(0, 3);

        console.log('Sorted reactions:', sortedReactions);

        const html = sortedReactions.map(([, data]) => `
            <div class="w-5 h-5 rounded-full bg-white border border-gray-200 flex items-center justify-center">
                <img src="${data.emoji}" alt="${data.label}" class="w-4 h-4">
            </div>
        `).join('');

        console.log('Generated HTML:', html);
        return html;
    }

    // Method to update comment count from external calls
    updateCommentCount(postId, newCount) {
        const postCard = document.querySelector(`[data-post-id="${postId}"]`);
        if (!postCard) return;

        // Update the comment count in action buttons
        const actionCommentElement = document.getElementById(`comments-count-${postId}`);
        if (actionCommentElement) {
            actionCommentElement.textContent = `${newCount} comment${newCount !== 1 ? 's' : ''}`;
        }

        // Update the summary bar
        const summaryCommentElement = document.getElementById(`comments-summary-${postId}`);
        if (summaryCommentElement) {
            summaryCommentElement.textContent = `${newCount} comment${newCount !== 1 ? 's' : ''}`;
        } else if (newCount > 0) {
            // Refresh the summary bar to include comments
            this.refreshSummaryBar(postCard);
        }
    }

    // Method to update share count from external calls
    updateShareCount(postId, newCount) {
        const postCard = document.querySelector(`[data-post-id="${postId}"]`);
        if (!postCard) return;

        // Update the share count in action buttons
        const actionShareElement = document.getElementById(`shares-count-${postId}`);
        if (actionShareElement) {
            actionShareElement.textContent = `${newCount} share${newCount !== 1 ? 's' : ''}`;
        }

        // Update the summary bar
        const summaryShareElement = document.getElementById(`shares-summary-${postId}`);
        if (summaryShareElement) {
            summaryShareElement.textContent = `${newCount} share${newCount !== 1 ? 's' : ''}`;
        } else if (newCount > 0) {
            // Refresh the summary bar to include shares
            this.refreshSummaryBar(postCard);
        }
    }

    // Helper method to refresh the entire summary bar
    refreshSummaryBar(postCard) {
        // This would ideally fetch fresh data from the server
        // For now, we'll just trigger a re-render with current data
        const postId = postCard.dataset.postId;

        // Get current reaction counts (this could be enhanced to fetch from server)
        const reactionButton = postCard.querySelector(`[data-target-id="${postId}"]`);
        if (reactionButton) {
            // Trigger a refresh by calling the update method with current data
            // In a real implementation, you might want to fetch fresh data from the server
            console.log('Summary bar refresh needed for post', postId);
        }
    }
}

// Initialize the reaction system when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.facebookReactionSystem = new FacebookReactionSystem();
});

// Global functions for external access
window.updatePostCommentCount = function(postId, newCount) {
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.updateCommentCount(postId, newCount);
    }
};

window.updatePostShareCount = function(postId, newCount) {
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.updateShareCount(postId, newCount);
    }
};

// Make it globally available
window.FacebookReactionSystem = FacebookReactionSystem;