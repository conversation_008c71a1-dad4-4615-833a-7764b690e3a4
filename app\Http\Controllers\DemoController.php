<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\Organization;
use Illuminate\Http\Request;

class DemoController extends Controller
{
    public function index()
    {
        // Get some sample data for the demo
        $organizations = Organization::with('creator')->latest()->take(3)->get();
        $groups = Group::with(['creator', 'organization'])->latest()->take(3)->get();
        $pageOrganizations = Organization::where('is_page_mode', true)->latest()->take(3)->get();
        
        return view('demo.index', compact('organizations', 'groups', 'pageOrganizations'));
    }
}
