<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrganizationFollower extends Model
{
    protected $fillable = [
        'organization_id',
        'user_id',
    ];

    /**
     * Get the organization being followed
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get the user who is following
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
