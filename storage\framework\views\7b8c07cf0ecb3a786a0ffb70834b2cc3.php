<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['post']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['post']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<!-- Comment Modal -->
<div id="commentModal-<?php echo e($post->id); ?>" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 hidden z-50" onclick="closeCommentModal(<?php echo e($post->id); ?>, event)">
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" style="height: 90vh; max-height: 90vh;" onclick="event.stopPropagation()">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                <?php if($post->organization): ?>
                    <?php echo e($post->organization->name); ?>'s post
                <?php else: ?>
                    <?php echo e($post->user->name); ?>'s post
                <?php endif; ?>
            </h3>
            <button onclick="closeCommentModal(<?php echo e($post->id); ?>)" class="text-gray-500 hover:text-gray-700 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Scrollable Content Area -->
        <div class="flex-1 overflow-y-auto min-h-0" style="max-height: calc(90vh - 140px);">

        <!-- Post Content -->
        <div class="p-4 border-b border-gray-200">
            <!-- Post Header -->
            <div class="flex items-center space-x-3 mb-4">
                <?php if($post->organization): ?>
                    <a href="<?php echo e(route('organizations.show', $post->organization)); ?>">
                        <img class="h-10 w-10 rounded-full"
                             src="<?php echo e($post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->organization->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($post->organization->name); ?>">
                    </a>
                    <div>
                        <a href="<?php echo e(route('organizations.show', $post->organization)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($post->organization->name); ?>

                        </a>
                        <p class="text-sm text-gray-500"><?php echo e($post->created_at->diffForHumans()); ?></p>
                    </div>
                <?php else: ?>
                    <a href="<?php echo e(route('profile.user', $post->user)); ?>">
                        <img class="h-10 w-10 rounded-full"
                             src="<?php echo e($post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($post->user->name); ?>">
                    </a>
                    <div>
                        <a href="<?php echo e(route('profile.user', $post->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($post->user->name); ?>

                        </a>
                        <p class="text-sm text-gray-500"><?php echo e($post->created_at->diffForHumans()); ?></p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Post Title and Content -->
            <h4 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e($post->title); ?></h4>
            <div class="text-gray-700 mb-4">
                <?php echo nl2br(e($post->content)); ?>

            </div>

            <!-- Post Images -->
            <?php if($post->images && count($post->images) > 0): ?>
                <div class="mb-4">
                    <?php if(count($post->images) == 1): ?>
                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($post->images[0])); ?>" 
                             alt="Post image" 
                             class="w-full rounded-lg max-h-96 object-cover">
                    <?php else: ?>
                        <div class="grid grid-cols-2 gap-2">
                            <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($index < 4): ?>
                                    <div class="relative">
                                        <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                             alt="Post image <?php echo e($index + 1); ?>" 
                                             class="w-full h-48 object-cover rounded-lg">
                                        <?php if($index == 3 && count($post->images) > 4): ?>
                                            <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                                                <span class="text-white text-xl font-bold">+<?php echo e(count($post->images) - 4); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Engagement Stats -->
        <div class="px-4 py-3 border-b border-gray-200">
            <div class="flex items-center justify-between text-gray-600 text-sm">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-1" id="modal-likes-section-<?php echo e($post->id); ?>" style="<?php echo e($post->likes->count() > 0 ? '' : 'display: none;'); ?>">
                        <div class="flex items-center">
                            <div class="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                        </div>
                        <span id="modal-likes-count-<?php echo e($post->id); ?>"><?php echo e($post->likes->count()); ?></span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="modal-comments-count-<?php echo e($post->id); ?>"><?php echo e($post->comments->count()); ?> comments</span>
                    <span id="modal-shares-count-<?php echo e($post->id); ?>"><?php echo e($post->shares->count()); ?> shares</span>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="px-4 py-3 border-b border-gray-200">
            <div class="flex items-center justify-around">
                <!-- Facebook-style Reactions -->
                <?php if (isset($component)) { $__componentOriginala21421101ecd1a67ffa8905ddbe200b3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.facebook-reactions','data' => ['target' => $post,'targetType' => 'post']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('facebook-reactions'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['target' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post),'target-type' => 'post']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $attributes = $__attributesOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__attributesOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3)): ?>
<?php $component = $__componentOriginala21421101ecd1a67ffa8905ddbe200b3; ?>
<?php unset($__componentOriginala21421101ecd1a67ffa8905ddbe200b3); ?>
<?php endif; ?>

                <button class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="font-medium">Comment</span>
                </button>

                <button onclick="openShareModal(<?php echo e($post->id); ?>)"
                        class="flex items-center space-x-2 text-gray-600 hover:text-green-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span class="font-medium">Share</span>
                </button>
            </div>
        </div>

            <!-- Comments Section -->
            <div>
            <!-- Sort Options -->
            <div class="px-4 py-3 border-b border-gray-200">
                <select id="comment-sort-<?php echo e($post->id); ?>" class="comment-sort-dropdown bg-white text-gray-900 border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-custom-green" data-post-id="<?php echo e($post->id); ?>">
                    <option value="most_relevant">Most relevant</option>
                    <option value="newest">Newest</option>
                    <option value="oldest">Oldest</option>
                </select>
            </div>

            <!-- Comments List -->
            <div class="comments-list divide-y divide-gray-200" id="comments-list-<?php echo e($post->id); ?>">
                <?php $__empty_1 = true; $__currentLoopData = $post->comments->whereNull('parent_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <?php if (isset($component)) { $__componentOriginal233fec21c88a27e408e4d4a4e1035b3d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal233fec21c88a27e408e4d4a4e1035b3d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal-comment-item','data' => ['comment' => $comment,'post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modal-comment-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['comment' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($comment),'post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal233fec21c88a27e408e4d4a4e1035b3d)): ?>
<?php $attributes = $__attributesOriginal233fec21c88a27e408e4d4a4e1035b3d; ?>
<?php unset($__attributesOriginal233fec21c88a27e408e4d4a4e1035b3d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal233fec21c88a27e408e4d4a4e1035b3d)): ?>
<?php $component = $__componentOriginal233fec21c88a27e408e4d4a4e1035b3d; ?>
<?php unset($__componentOriginal233fec21c88a27e408e4d4a4e1035b3d); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="no-comments text-gray-600 text-center py-12 px-4">
                        <div class="max-w-sm mx-auto">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-700 mb-2">No comments yet</h3>
                            <p class="text-gray-500">Be the first to share what you think!</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Close the scrollable content area -->
        </div>

        <!-- Add Comment Form - Fixed at bottom -->
        <?php if(auth()->guard()->check()): ?>
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
            <form class="comment-form" data-post-id="<?php echo e($post->id); ?>" data-parent-id="">
                    <?php echo csrf_field(); ?>
                    <div class="flex space-x-3">
                        <div class="flex-shrink-0">
                            <img class="h-8 w-8 rounded-full"
                                 src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e(auth()->user()->name); ?>">
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="relative">
                                <textarea name="content" rows="1"
                                          placeholder="Write a comment..."
                                          class="w-full bg-gray-50 text-gray-900 border-2 border-gray-300 rounded-full px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none"
                                          style="min-height: 40px;" required></textarea>
                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="modal-comment-submit-btn-<?php echo e($post->id); ?>">
                                    <button type="submit"
                                            class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
        </div>
        <?php else: ?>
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white text-center">
            <p class="text-gray-700 mb-2">Please log in to comment</p>
            <a href="<?php echo e(route('login')); ?>" class="text-custom-green hover:text-green-400 font-semibold">Log in</a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/components/comment-modal.blade.php ENDPATH**/ ?>