<!-- Mobile Left Sidebar -->
<aside class="fixed top-16 left-0 z-40 w-64 h-[calc(100vh-4rem)] bg-custom-darkest shadow-lg border-r border-custom-second-darkest transform -translate-x-full transition-transform duration-300 ease-in-out overflow-y-auto custom-scrollbar"
       x-data="{ open: false }"
       x-on:toggle-sidebar.window="open = !open"
       :class="{ 'translate-x-0': open }">

    <div class="flex flex-col h-full">
        <!-- Profile Section -->
        <div class="px-6 py-4 border-b border-custom-second-darkest">
            <div class="flex items-center space-x-3">
                <img class="h-12 w-12 rounded-full" src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=222831'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-custom-lightest truncate"><?php echo e(auth()->user()->name); ?></p>
                    <p class="text-sm text-custom-lightest opacity-75 truncate"><?php echo e(ucfirst(str_replace('_', ' ', auth()->user()->role))); ?></p>
                    <?php if(auth()->user()->student_id): ?>
                        <p class="text-xs text-custom-lightest opacity-60"><?php echo e(auth()->user()->student_id); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
            <!-- Dashboard -->
            <a href="<?php echo e(route('dashboard')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('dashboard') ? 'bg-custom-green text-custom-darkest' : 'text-custom-lightest hover:bg-custom-second-darkest'); ?>">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
                </svg>
                Dashboard
            </a>

            <!-- Campus Announcements -->
            <a href="<?php echo e(route('announcements')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('announcements*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-lightest hover:bg-custom-second-darkest'); ?>">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                </svg>
                Campus Announcements
            </a>

            <!-- My Organizations -->
            <div x-data="{ expanded: <?php echo e(request()->routeIs('organizations*') ? 'true' : 'false'); ?> }">
                <button @click="expanded = !expanded" class="flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('organizations*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-lightest hover:bg-custom-second-darkest'); ?>">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        My Organizations
                    </div>
                    <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': expanded }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                </button>

                <div x-show="expanded" x-transition class="ml-8 mt-2 space-y-1">
                    <a href="<?php echo e(route('organizations.index')); ?>" class="block px-3 py-2 text-sm text-custom-lightest opacity-75 hover:text-custom-lightest hover:bg-custom-second-darkest rounded-lg">All Organizations</a>
                    <a href="<?php echo e(route('organizations.my')); ?>" class="block px-3 py-2 text-sm text-custom-lightest opacity-75 hover:text-custom-lightest hover:bg-custom-second-darkest rounded-lg">My Memberships</a>
                    <?php if(auth()->user()->hasManagementAccess()): ?>
                        <a href="<?php echo e(route('organizations.create')); ?>" class="block px-3 py-2 text-sm text-custom-lightest opacity-75 hover:text-custom-lightest hover:bg-custom-second-darkest rounded-lg">Create Organization</a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Find Scholarships -->
            <a href="<?php echo e(route('scholarships.index')); ?>" class="flex items-center px-3 py-2 text-sm font-medium rounded-lg <?php echo e(request()->routeIs('scholarships*') ? 'bg-custom-green text-custom-darkest' : 'text-custom-lightest hover:bg-custom-second-darkest'); ?>">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Find Scholarships
            </a>

            <!-- Messages (Optional) -->
            <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-custom-lightest rounded-lg hover:bg-custom-second-darkest">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                Messages
                <span class="ml-auto bg-custom-green text-custom-darkest text-xs font-medium px-2 py-0.5 rounded-full">3</span>
            </a>

            <?php if(auth()->user()->hasManagementAccess()): ?>
                <!-- Admin/Management Section -->
                <div class="pt-4 mt-4 border-t border-custom-second-darkest">
                    <p class="px-3 text-xs font-semibold text-custom-lightest opacity-60 uppercase tracking-wider">Management</p>

                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="flex items-center px-3 py-2 mt-2 text-sm font-medium text-custom-lightest rounded-lg hover:bg-custom-second-darkest">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                        Admin Panel
                    </a>
                </div>
            <?php endif; ?>
        </nav>

        <!-- Logout Button -->
        <div class="p-4 border-t border-custom-second-darkest">
            <form method="POST" action="<?php echo e(route('logout')); ?>">
                <?php echo csrf_field(); ?>
                <button type="submit" class="flex items-center w-full px-3 py-2 text-sm font-medium text-custom-darkest rounded-lg hover:bg-custom-second-darkest">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Logout
                </button>
            </form>
        </div>
    </div>
</aside>

<!-- Mobile sidebar overlay -->
<div x-data="{ open: false }"
     x-on:toggle-sidebar.window="open = !open"
     x-show="open"
     x-transition:enter="transition-opacity ease-linear duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition-opacity ease-linear duration-300"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     class="fixed inset-0 bg-custom-darkest bg-opacity-75 z-30 lg:hidden"
     @click="$dispatch('toggle-sidebar')">
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/unilink-sidebar.blade.php ENDPATH**/ ?>