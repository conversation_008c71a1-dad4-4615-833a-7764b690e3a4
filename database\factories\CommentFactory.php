<?php

namespace Database\Factories;

use App\Models\Comment;
use App\Models\User;
use App\Models\Post;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Comment>
 */
class CommentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'content' => fake()->paragraph(),
            'user_id' => User::factory(),
            'commentable_type' => Post::class,
            'commentable_id' => Post::factory(),
            'parent_id' => null,
        ];
    }

    /**
     * Indicate that the comment is a reply to another comment.
     */
    public function reply(Comment $parentComment = null): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentComment?->id ?? Comment::factory(),
            'commentable_type' => $parentComment?->commentable_type ?? Post::class,
            'commentable_id' => $parentComment?->commentable_id ?? Post::factory(),
        ]);
    }

    /**
     * Indicate that the comment belongs to a specific post.
     */
    public function forPost(Post $post): static
    {
        return $this->state(fn (array $attributes) => [
            'commentable_type' => Post::class,
            'commentable_id' => $post->id,
        ]);
    }
}
