<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attachments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_id')->constrained()->onDelete('cascade');
            $table->string('filename'); // Generated filename
            $table->string('original_filename'); // Original uploaded filename
            $table->string('file_path'); // Storage path
            $table->bigInteger('file_size'); // File size in bytes
            $table->string('mime_type'); // MIME type
            $table->string('file_type'); // Extension (pdf, jpg, etc.)
            $table->timestamps();

            $table->index('post_id');
            $table->index('file_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attachments');
    }
};
