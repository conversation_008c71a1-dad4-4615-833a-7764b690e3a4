<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Organizations</h1>
                <p class="text-gray-600 mt-1">Manage your organization memberships and invitations</p>
            </div>
            <a href="{{ route('organizations.index') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Browse Organizations
            </a>
        </div>
    </div>

    <!-- Pending Invitations -->
    @if($pendingInvitations->count() > 0)
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Pending Invitations</h2>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="space-y-3">
                    @foreach($pendingInvitations as $organization)
                        <div class="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    @if($organization->logo)
                                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-8 h-8 rounded-lg object-cover">
                                    @else
                                        <span class="text-blue-600 font-semibold text-sm">{{ substr($organization->name, 0, 2) }}</span>
                                    @endif
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ $organization->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $organization->active_members_count }} members</p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <form action="{{ route('organizations.join.accept', $organization) }}" method="POST" class="inline">
                                    @csrf
                                    <button type="submit" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
                                        Accept
                                    </button>
                                </form>
                                <form action="{{ route('organizations.join.decline', $organization) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                        Decline
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- My Organizations -->
    <div class="mb-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">My Organizations ({{ $myOrganizations->count() }})</h2>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($myOrganizations as $organization)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                <!-- Cover Image -->
                <div class="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                    @if($organization->cover_image)
                        <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->cover_image) }}" alt="{{ $organization->name }}" class="w-full h-full object-cover">
                    @endif
                    
                    <!-- Role Badge -->
                    <div class="absolute top-2 right-2">
                        @php
                            $role = $organization->pivot->role;
                            $badgeColor = match($role) {
                                'president' => 'bg-purple-100 text-purple-800',
                                'officer' => 'bg-blue-100 text-blue-800',
                                default => 'bg-gray-100 text-gray-800'
                            };
                        @endphp
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $badgeColor }}">
                            {{ ucfirst($role) }}
                        </span>
                    </div>
                    
                    <!-- Logo -->
                    <div class="absolute -bottom-6 left-4">
                        <div class="w-12 h-12 bg-white rounded-lg shadow-md flex items-center justify-center">
                            @if($organization->logo)
                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($organization->logo) }}" alt="{{ $organization->name }}" class="w-10 h-10 rounded-lg object-cover">
                            @else
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <span class="text-blue-600 font-semibold text-sm">{{ substr($organization->name, 0, 2) }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="p-4 pt-8">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-lg font-semibold text-gray-900 truncate">{{ $organization->name }}</h3>
                        <span class="text-sm text-gray-500">{{ $organization->active_members_count }} members</span>
                    </div>
                    
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ Str::limit($organization->description, 100) }}</p>
                    
                    <!-- Membership Info -->
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                        </svg>
                        Joined {{ $organization->pivot->joined_at ? $organization->pivot->joined_at->format('M Y') : 'Recently' }}
                    </div>

                    <!-- Actions -->
                    <div class="flex gap-2">
                        <a href="{{ route('organizations.show', $organization) }}" class="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            View
                        </a>
                        
                        @if(in_array($organization->pivot->role, ['officer', 'president']) || auth()->user()->isAdmin())
                            <a href="{{ route('organizations.edit', $organization) }}" class="flex-1 bg-gray-600 text-white text-center py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Manage
                            </a>
                        @endif
                        
                        @if($organization->pivot->role !== 'president')
                            <form action="{{ route('organizations.leave', $organization) }}" method="POST" class="flex-1" onsubmit="return confirm('Are you sure you want to leave this organization?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="w-full bg-red-600 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                                    Leave
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No organizations yet</h3>
                <p class="mt-1 text-sm text-gray-500">Join your first organization to get started.</p>
                <div class="mt-6">
                    <a href="{{ route('organizations.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Browse Organizations
                    </a>
                </div>
            </div>
        @endforelse
    </div>
</x-unilink-layout>
