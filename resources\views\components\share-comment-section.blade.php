@props(['share', 'showInline' => false])

<div class="share-comment-section bg-white border-t border-gray-100">
    <!-- Comment Form -->
    @auth
        <div class="p-4 border-b border-gray-50">
            <form class="share-comment-form" data-share-id="{{ $share->id }}">
                @csrf
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full ring-2 ring-white shadow-sm"
                             src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ auth()->user()->name }}">
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="relative">
                            <textarea name="content" rows="1"
                                      placeholder="Write a comment..."
                                      class="w-full px-4 py-3 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200 placeholder-gray-400"
                                      required></textarea>
                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 transition-opacity duration-200" id="comment-submit-btn-{{ $share->id }}">
                                <button type="submit"
                                        class="p-2 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    @endauth

    <!-- Comments List -->
    <div class="divide-y divide-gray-100" id="share-comments-list-{{ $share->id }}">
        @forelse($share->comments()->whereNull('parent_id')->with(['user', 'replies.user', 'likes'])->latest()->get() as $comment)
            <div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150" data-comment-id="{{ $comment->id }}">
                <div class="flex space-x-3">
                    <a href="{{ route('profile.user', $comment->user) }}" class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                             src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                             alt="{{ $comment->user->name }}">
                    </a>
                    <div class="flex-1 min-w-0">
                        <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                            <div class="flex items-center space-x-2 mb-2">
                                <a href="{{ route('profile.user', $comment->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                    {{ $comment->user->name }}
                                </a>
                                <span class="text-xs text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                                @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                                    <div class="relative ml-auto" x-data="{ open: false }">
                                        <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                            </svg>
                                        </button>
                                        <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100"
                                             x-transition:enter-start="transform opacity-0 scale-95"
                                             x-transition:enter-end="transform opacity-100 scale-100"
                                             x-transition:leave="transition ease-in duration-75"
                                             x-transition:leave-start="transform opacity-100 scale-100"
                                             x-transition:leave-end="transform opacity-0 scale-95"
                                             class="absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                            <button onclick="editShareComment({{ $comment->id }})"
                                                    class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                <span>Edit</span>
                                            </button>
                                            <button onclick="deleteShareComment({{ $comment->id }})"
                                                    class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                <span>Delete</span>
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="comment-content" id="comment-content-{{ $comment->id }}">
                                <p class="text-gray-700 text-sm leading-relaxed">{!! nl2br(e($comment->content)) !!}</p>
                            </div>

                            <!-- Edit Form (hidden by default) -->
                            <div class="edit-comment-form hidden mt-3" id="edit-comment-form-{{ $comment->id }}">
                                <form class="share-comment-edit-form" data-comment-id="{{ $comment->id }}">
                                    @csrf
                                    @method('PUT')
                                    <textarea name="content" rows="2"
                                              class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">{{ $comment->content }}</textarea>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="cancelEditShareComment({{ $comment->id }})"
                                                class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                        <button type="submit"
                                                class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                            Save
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Comment Actions -->
                        <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                            @auth
                                <button onclick="toggleShareCommentLike({{ $comment->id }})"
                                        class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors group"
                                        id="share-comment-like-btn-{{ $comment->id }}">
                                    @php
                                        $isLiked = $comment->isLikedBy(auth()->user());
                                    @endphp
                                    <svg class="w-4 h-4 {{ $isLiked ? 'text-red-600 fill-current' : '' }} group-hover:scale-110 transition-transform duration-200"
                                         fill="{{ $isLiked ? 'currentColor' : 'none' }}"
                                         stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    <span id="share-comment-like-count-{{ $comment->id }}" class="font-medium">{{ $comment->likes->count() }}</span>
                                </button>
                            @else
                                <span class="flex items-center space-x-1 text-gray-400">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                    </svg>
                                    <span class="font-medium">{{ $comment->likes->count() }}</span>
                                </span>
                            @endauth

                            @auth
                                <button onclick="toggleShareCommentReply({{ $comment->id }})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                                    </svg>
                                    <span class="font-medium">Reply</span>
                                </button>
                            @endauth

                            @if($comment->created_at != $comment->updated_at)
                                <span class="text-xs text-gray-400 italic">• edited</span>
                            @endif
                        </div>

                        <!-- Reply Form (hidden by default) -->
                        <div class="reply-form hidden mt-3 ml-4" id="share-reply-form-{{ $comment->id }}">
                            <form class="share-comment-reply-form" data-parent-id="{{ $comment->id }}" data-share-id="{{ $share->id }}">
                                @csrf
                                <div class="flex space-x-3">
                                    <div class="flex-shrink-0">
                                        <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                             src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                             alt="{{ auth()->user()->name ?? 'User' }}">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="relative">
                                            <textarea name="content" rows="1"
                                                      placeholder="Write a reply..."
                                                      class="w-full px-3 py-2 border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200"
                                                      required></textarea>
                                        </div>
                                        <div class="mt-2 flex justify-end space-x-2">
                                            <button type="button" onclick="cancelShareCommentReply({{ $comment->id }})"
                                                    class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                            <button type="submit"
                                                    class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                                Reply
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Replies -->
                        @if($comment->replies->count() > 0)
                            <div class="nested-comments mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4">
                                @foreach($comment->replies as $reply)
                                    <div class="comment-item" data-comment-id="{{ $reply->id }}">
                                        <div class="flex space-x-3">
                                            <a href="{{ route('profile.user', $reply->user) }}" class="flex-shrink-0">
                                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                                     src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                     alt="{{ $reply->user->name }}">
                                            </a>
                                            <div class="flex-1 min-w-0">
                                                <div class="bg-gray-50/50 rounded-xl p-3 shadow-sm border border-gray-100">
                                                    <div class="flex items-center space-x-2 mb-2">
                                                        <a href="{{ route('profile.user', $reply->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                                            {{ $reply->user->name }}
                                                        </a>
                                                        <span class="text-xs text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                                        @if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin()))
                                                            <div class="relative ml-auto" x-data="{ open: false }">
                                                                <button @click="open = !open" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                                                    </svg>
                                                                </button>
                                                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100"
                                                                     x-transition:enter-start="transform opacity-0 scale-95"
                                                                     x-transition:enter-end="transform opacity-100 scale-100"
                                                                     x-transition:leave="transition ease-in duration-75"
                                                                     x-transition:leave-start="transform opacity-100 scale-100"
                                                                     x-transition:leave-end="transform opacity-0 scale-95"
                                                                     class="absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                                                    <button onclick="editShareComment({{ $reply->id }})"
                                                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                                        </svg>
                                                                        <span>Edit</span>
                                                                    </button>
                                                                    <button onclick="deleteShareComment({{ $reply->id }})"
                                                                            class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                        </svg>
                                                                        <span>Delete</span>
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>

                                                    <div class="comment-content" id="comment-content-{{ $reply->id }}">
                                                        <p class="text-gray-700 text-sm leading-relaxed">{!! nl2br(e($reply->content)) !!}</p>
                                                    </div>

                                                    <!-- Edit Form for Reply (hidden by default) -->
                                                    <div class="edit-comment-form hidden mt-3" id="edit-comment-form-{{ $reply->id }}">
                                                        <form class="share-comment-edit-form" data-comment-id="{{ $reply->id }}">
                                                            @csrf
                                                            @method('PUT')
                                                            <textarea name="content" rows="2"
                                                                      class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">{{ $reply->content }}</textarea>
                                                            <div class="mt-2 flex justify-end space-x-2">
                                                                <button type="button" onclick="cancelEditShareComment({{ $reply->id }})"
                                                                        class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                                                <button type="submit"
                                                                        class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                                                    Save
                                                                </button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>

                                                <!-- Reply Actions -->
                                                <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                                                    @auth
                                                        <button onclick="toggleShareCommentLike({{ $reply->id }})"
                                                                class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors group"
                                                                id="share-comment-like-btn-{{ $reply->id }}">
                                                            @php
                                                                $isReplyLiked = $reply->isLikedBy(auth()->user());
                                                            @endphp
                                                            <svg class="w-4 h-4 {{ $isReplyLiked ? 'text-red-600 fill-current' : '' }} group-hover:scale-110 transition-transform duration-200"
                                                                 fill="{{ $isReplyLiked ? 'currentColor' : 'none' }}"
                                                                 stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                            </svg>
                                                            <span id="share-comment-like-count-{{ $reply->id }}" class="font-medium">{{ $reply->likes->count() }}</span>
                                                        </button>
                                                    @else
                                                        <span class="flex items-center space-x-1 text-gray-400">
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                            </svg>
                                                            <span class="font-medium">{{ $reply->likes->count() }}</span>
                                                        </span>
                                                    @endauth

                                                    @if($reply->created_at != $reply->updated_at)
                                                        <span class="text-xs text-gray-400 italic">• edited</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="no-comments text-gray-500 text-center py-12 px-4">
                <div class="max-w-sm mx-auto">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                    <p class="text-gray-500">Be the first to share your thoughts!</p>
                </div>
            </div>
        @endforelse
    </div>
</div>

<style>
/* Comment Form Enhancements */
.share-comment-section .share-comment-form textarea:focus {
    min-height: 100px;
    border-radius: 1rem;
    transition: all 0.3s ease;
    background-color: white;
}

.share-comment-section .share-comment-form textarea:focus + div #comment-submit-btn-{{ $share->id ?? '' }} {
    opacity: 1;
}

/* Reply Form Styling */
.share-comment-section .reply-form textarea:focus {
    min-height: 80px;
    transition: min-height 0.2s ease;
}

/* Comment Item Hover Effects */
.share-comment-section .comment-item {
    transition: all 0.2s ease;
}

.share-comment-section .comment-item:hover {
    transform: translateY(-1px);
}

/* Action Button Enhancements */
.share-comment-section .comment-actions button {
    transition: all 0.2s ease;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
}

.share-comment-section .comment-actions button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Nested Comments Styling */
.share-comment-section .nested-comments {
    position: relative;
}

.share-comment-section .nested-comments::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e5e7eb, transparent);
}

/* Avatar Enhancements */
.share-comment-section img {
    transition: all 0.2s ease;
}

.share-comment-section img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Form Input Enhancements */
.share-comment-section textarea {
    transition: all 0.2s ease;
}

.share-comment-section textarea:hover {
    border-color: #d1d5db;
}

/* Button Enhancements */
.share-comment-section button[type="submit"] {
    transition: all 0.2s ease;
}

.share-comment-section button[type="submit"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Dropdown Menu Enhancements */
.share-comment-section [x-show="open"] {
    backdrop-filter: blur(4px);
}

/* Loading States */
.share-comment-section .loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Smooth Animations */
.share-comment-section * {
    scroll-behavior: smooth;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced comment form interactions
    const commentForms = document.querySelectorAll('.share-comment-form');

    commentForms.forEach(form => {
        const textarea = form.querySelector('textarea[name="content"]');
        const submitBtn = form.querySelector('button[type="submit"]');

        if (textarea && submitBtn) {
            // Show/hide submit button based on content
            textarea.addEventListener('input', function() {
                const submitBtnContainer = form.querySelector('[id^="comment-submit-btn-"]');
                if (this.value.trim().length > 0) {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '1';
                    }
                } else {
                    if (submitBtnContainer) {
                        submitBtnContainer.style.opacity = '0';
                    }
                }
            });

            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });

            // Focus effects
            textarea.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            textarea.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.parentElement.classList.remove('focused');
                }
            });
        }
    });

    // Enhanced reply form interactions
    const replyForms = document.querySelectorAll('.share-comment-reply-form');

    replyForms.forEach(form => {
        const textarea = form.querySelector('textarea[name="content"]');

        if (textarea) {
            // Auto-resize textarea
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });
        }
    });

    // Smooth scroll to new comments
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList.contains('comment-item')) {
                        node.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }
        });
    });

    // Observe comment lists for new additions
    const commentLists = document.querySelectorAll('[id^="share-comments-list-"]');
    commentLists.forEach(list => {
        observer.observe(list, { childList: true, subtree: true });
    });
});
</script>
