<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Reaction extends Model
{
    protected $fillable = [
        'reactable_type',
        'reactable_id',
        'user_id',
        'type',
    ];

    // Reaction types with their emoji images and colors
    // Emoji images are stored in public/assets/emojis/ as animated GIFs
    public static $types = [
        'like' => [
            'emoji' => '/assets/emojis/like.gif',
            'label' => 'Like',
            'color' => 'text-blue-600',
            'bg_color' => 'bg-blue-50',
        ],
        'love' => [
            'emoji' => '/assets/emojis/love.gif',
            'label' => 'Love',
            'color' => 'text-red-600',
            'bg_color' => 'bg-red-50',
        ],
        'haha' => [
            'emoji' => '/assets/emojis/haha.gif',
            'label' => 'Haha',
            'color' => 'text-yellow-600',
            'bg_color' => 'bg-yellow-50',
        ],
        'wow' => [
            'emoji' => '/assets/emojis/wow.gif',
            'label' => 'Wow',
            'color' => 'text-orange-600',
            'bg_color' => 'bg-orange-50',
        ],
        'sad' => [
            'emoji' => '/assets/emojis/sad.gif',
            'label' => 'Sad',
            'color' => 'text-yellow-600',
            'bg_color' => 'bg-yellow-50',
        ],
        'angry' => [
            'emoji' => '/assets/emojis/angry.gif',
            'label' => 'Angry',
            'color' => 'text-red-600',
            'bg_color' => 'bg-red-50',
        ],
    ];

    /**
     * Get the reactable model (post, comment, etc.)
     */
    public function reactable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who made this reaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get reaction details by type
     */
    public static function getReactionDetails($type)
    {
        return self::$types[$type] ?? self::$types['like'];
    }

    /**
     * Get all available reaction types
     */
    public static function getAvailableTypes()
    {
        return array_keys(self::$types);
    }
}
