<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Reaction extends Model
{
    protected $fillable = [
        'reactable_type',
        'reactable_id',
        'user_id',
        'type',
    ];

    // Reaction types with their emoji and colors
    public static $types = [
        'like' => [
            'emoji' => '👍',
            'label' => 'Like',
            'color' => 'text-blue-600',
            'bg_color' => 'bg-blue-50',
        ],
        'love' => [
            'emoji' => '❤️',
            'label' => 'Love',
            'color' => 'text-red-600',
            'bg_color' => 'bg-red-50',
        ],
        'haha' => [
            'emoji' => '😂',
            'label' => 'Haha',
            'color' => 'text-yellow-600',
            'bg_color' => 'bg-yellow-50',
        ],
        'wow' => [
            'emoji' => '😮',
            'label' => 'Wow',
            'color' => 'text-orange-600',
            'bg_color' => 'bg-orange-50',
        ],
        'sad' => [
            'emoji' => '😢',
            'label' => 'Sad',
            'color' => 'text-yellow-600',
            'bg_color' => 'bg-yellow-50',
        ],
        'angry' => [
            'emoji' => '😡',
            'label' => 'Angry',
            'color' => 'text-red-600',
            'bg_color' => 'bg-red-50',
        ],
    ];

    /**
     * Get the reactable model (post, comment, etc.)
     */
    public function reactable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who made this reaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get reaction details by type
     */
    public static function getReactionDetails($type)
    {
        return self::$types[$type] ?? self::$types['like'];
    }

    /**
     * Get all available reaction types
     */
    public static function getAvailableTypes()
    {
        return array_keys(self::$types);
    }
}
