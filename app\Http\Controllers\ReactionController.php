<?php

namespace App\Http\Controllers;

use App\Models\Reaction;
use App\Models\Post;
use App\Models\Comment;
use App\Models\Share;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReactionController extends Controller
{
    /**
     * Store or update a reaction
     */
    public function store(Request $request)
    {
        $request->validate([
            'target_id' => 'required',
            'target_type' => 'required|string|in:post,comment,share',
            'reaction_type' => 'nullable|string|in:like,love,haha,wow,sad,angry',
        ]);

        $user = Auth::user();
        $targetId = $request->target_id;
        $targetType = $request->target_type;
        $reactionType = $request->reaction_type;

        // Handle demo targets (for testing)
        if (str_starts_with($targetId, 'demo-')) {
            return $this->handleDemoReaction($targetId, $targetType, $reactionType, $user);
        }

        // Get the target model
        $target = $this->getTargetModel($targetType, $targetId);

        if (!$target) {
            return response()->json(['success' => false, 'message' => 'Target not found'], 404);
        }

        // Find existing reaction
        $existingReaction = Reaction::where([
            'reactable_type' => $this->getReactableType($targetType),
            'reactable_id' => $targetId,
            'user_id' => $user->id,
        ])->first();

        if ($reactionType === null) {
            // Remove reaction if it exists
            if ($existingReaction) {
                $existingReaction->delete();
            }
            $reaction = null;
        } else {
            // Create or update reaction
            if ($existingReaction) {
                $existingReaction->update(['type' => $reactionType]);
                $reaction = $existingReaction;
            } else {
                $reaction = Reaction::create([
                    'reactable_type' => $this->getReactableType($targetType),
                    'reactable_id' => $targetId,
                    'user_id' => $user->id,
                    'type' => $reactionType,
                ]);
            }
        }

        // Get reaction counts
        $counts = $this->getReactionCounts($targetType, $targetId);

        // Format reaction data for frontend
        $reactionData = null;
        if ($reaction) {
            $details = Reaction::getReactionDetails($reaction->type);
            $reactionData = [
                'type' => $reaction->type,
                'emoji' => $details['emoji'],
                'label' => $details['label'],
                'color' => $details['color'],
                'bg_color' => $details['bg_color'],
            ];
        }

        return response()->json([
            'success' => true,
            'reaction' => $reactionData,
            'counts' => $counts,
        ]);
    }

    /**
     * Get reaction counts for a target
     */
    public function getCounts(Request $request)
    {
        $request->validate([
            'target_id' => 'required|integer',
            'target_type' => 'required|string|in:post,comment,share',
        ]);

        $counts = $this->getReactionCounts($request->target_type, $request->target_id);

        return response()->json([
            'success' => true,
            'counts' => $counts,
        ]);
    }

    /**
     * Get complete post summary data (reactions, comments, shares)
     */
    public function getPostSummary(Request $request)
    {
        $request->validate([
            'post_id' => 'required|integer',
        ]);

        $post = \App\Models\Post::find($request->post_id);
        if (!$post) {
            return response()->json(['success' => false, 'message' => 'Post not found'], 404);
        }

        // Get reaction data
        $reactionCounts = $this->getReactionCounts('post', $post->id);

        // Get top 3 reaction types for display
        $topReactions = $post->reactions()
            ->select('type', \DB::raw('count(*) as count'))
            ->groupBy('type')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get()
            ->map(function ($reaction) {
                $details = \App\Models\Reaction::getReactionDetails($reaction->type);
                return [
                    'type' => $reaction->type,
                    'count' => $reaction->count,
                    'emoji' => $details['emoji'],
                    'label' => $details['label'],
                ];
            });

        // Get comment and share counts
        $commentCount = $post->comments()->count();
        $shareCount = $post->shares()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'reactions' => [
                    'total' => $reactionCounts['total'],
                    'top_reactions' => $topReactions,
                    'by_type' => $reactionCounts['by_type'],
                ],
                'comments' => [
                    'total' => $commentCount,
                ],
                'shares' => [
                    'total' => $shareCount,
                ],
            ],
        ]);
    }

    /**
     * Get the target model instance
     */
    private function getTargetModel($targetType, $targetId)
    {
        switch ($targetType) {
            case 'post':
                return Post::find($targetId);
            case 'comment':
                return Comment::find($targetId);
            case 'share':
                return Share::find($targetId);
            default:
                return null;
        }
    }

    /**
     * Get the reactable type for the database
     */
    private function getReactableType($targetType)
    {
        $mapping = [
            'post' => 'App\Models\Post',
            'comment' => 'App\Models\Comment',
            'share' => 'App\Models\Share',
        ];

        return $mapping[$targetType] ?? null;
    }

    /**
     * Get reaction counts for a target
     */
    private function getReactionCounts($targetType, $targetId)
    {
        $reactableType = $this->getReactableType($targetType);
        
        $reactions = Reaction::where([
            'reactable_type' => $reactableType,
            'reactable_id' => $targetId,
        ])->get();

        $counts = [
            'total' => $reactions->count(),
            'by_type' => [],
        ];

        foreach (Reaction::getAvailableTypes() as $type) {
            $count = $reactions->where('type', $type)->count();
            if ($count > 0) {
                $details = Reaction::getReactionDetails($type);
                $counts['by_type'][$type] = [
                    'count' => $count,
                    'emoji' => $details['emoji'],
                    'label' => $details['label'],
                ];
            }
        }

        return $counts;
    }

    /**
     * Handle demo reactions (for testing purposes)
     */
    private function handleDemoReaction($targetId, $targetType, $reactionType, $user)
    {
        // For demo purposes, we'll use session storage
        $sessionKey = "demo_reactions_{$targetId}_{$user->id}";
        $currentReaction = session($sessionKey);

        if ($reactionType === null) {
            // Remove reaction
            session()->forget($sessionKey);
            $reaction = null;
        } else {
            // Set reaction
            session([$sessionKey => $reactionType]);
            $reaction = $reactionType;
        }

        // Format reaction data for frontend
        $reactionData = null;
        if ($reaction) {
            $details = Reaction::getReactionDetails($reaction);
            $reactionData = [
                'type' => $reaction,
                'emoji' => $details['emoji'],
                'label' => $details['label'],
                'color' => $details['color'],
                'bg_color' => $details['bg_color'],
            ];
        }

        // Mock reaction counts for demo
        $counts = [
            'total' => $reaction ? 1 : 0,
            'by_type' => $reaction ? [
                $reaction => [
                    'count' => 1,
                    'emoji' => $reactionData['emoji'],
                    'label' => $reactionData['label'],
                ]
            ] : [],
        ];

        return response()->json([
            'success' => true,
            'reaction' => $reactionData,
            'counts' => $counts,
        ]);
    }
}
