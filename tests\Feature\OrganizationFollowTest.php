<?php

use App\Models\Organization;
use App\Models\User;

test('user can follow organization', function () {
    $user = User::factory()->create();
    $organization = Organization::factory()->create([
        'is_page_mode' => true
    ]);

    $response = $this->actingAs($user)
        ->post("/organizations/{$organization->slug}/follow");

    $response->assertRedirect();
    expect($organization->isFollowedBy($user))->toBeTrue();
});

test('user can unfollow organization', function () {
    $user = User::factory()->create();
    $organization = Organization::factory()->create([
        'is_page_mode' => true
    ]);

    // First follow
    $organization->followers()->attach($user->id);
    expect($organization->isFollowedBy($user))->toBeTrue();

    // Then unfollow
    $response = $this->actingAs($user)
        ->delete("/organizations/{$organization->slug}/unfollow");

    $response->assertRedirect();
    expect($organization->fresh()->isFollowedBy($user))->toBeFalse();
});

test('user cannot follow same organization twice', function () {
    $user = User::factory()->create();
    $organization = Organization::factory()->create([
        'is_page_mode' => true
    ]);

    // First follow
    $this->actingAs($user)
        ->post("/organizations/{$organization->slug}/follow");

    // Try to follow again
    $response = $this->actingAs($user)
        ->post("/organizations/{$organization->slug}/follow");

    $response->assertRedirect();
    $response->assertSessionHas('error');
});

test('organization page mode shows correct view', function () {
    $organization = Organization::factory()->create([
        'is_page_mode' => true
    ]);

    $response = $this->get("/organizations/{$organization->slug}");

    $response->assertStatus(200);
    $response->assertViewIs('organizations.page');
});

test('organization regular mode shows correct view', function () {
    $organization = Organization::factory()->create([
        'is_page_mode' => false
    ]);

    $response = $this->get("/organizations/{$organization->slug}");

    $response->assertStatus(200);
    $response->assertViewIs('organizations.show');
});
