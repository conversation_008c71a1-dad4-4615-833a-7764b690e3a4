<?php

namespace Database\Seeders;

use App\Models\Scholarship;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ScholarshipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users who can create scholarships
        $admin = User::where('role', 'admin')->first();
        $orgOfficer = User::where('role', 'org_officer')->first();

        $scholarships = [
            [
                'title' => 'Merit-Based Academic Excellence Scholarship',
                'description' => 'This scholarship recognizes outstanding academic achievement and is awarded to students who demonstrate exceptional scholastic performance. Recipients must maintain high academic standards and show commitment to their educational goals.',
                'amount' => 5000.00,
                'provider' => 'University Foundation',
                'requirements' => 'Applicants must be enrolled full-time, maintain a minimum GPA of 3.5, and demonstrate financial need. Must be in good academic standing with no disciplinary actions.',
                'application_process' => 'Submit completed application form along with official transcripts, two letters of recommendation, and a personal statement. Applications are reviewed by the scholarship committee and recipients are notified within 4 weeks.',
                'application_url' => 'https://university.edu/scholarships/merit-based',
                'deadline' => now()->addMonths(2)->format('Y-m-d'),
                'eligibility_criteria' => [
                    'Minimum GPA of 3.5',
                    'Full-time enrollment',
                    'Demonstrated financial need',
                    'Good academic standing'
                ],
                'required_documents' => [
                    'Official transcripts',
                    'Two letters of recommendation',
                    'Personal statement (500 words)',
                    'FAFSA form'
                ],
                'status' => 'active',
                'created_by' => $admin?->id ?? 1,
            ],
            [
                'title' => 'STEM Innovation Scholarship',
                'description' => 'Supporting the next generation of innovators in Science, Technology, Engineering, and Mathematics. This scholarship aims to encourage students pursuing STEM fields and foster innovation in these critical areas.',
                'amount' => 3500.00,
                'provider' => 'Tech Industry Partnership',
                'requirements' => 'Must be majoring in a STEM field, demonstrate innovation through projects or research, and show commitment to using technology for positive impact.',
                'application_process' => 'Complete online application including project portfolio, academic records, and essay on innovation in STEM. Finalists may be invited for virtual interviews.',
                'application_url' => 'https://steminnovation.org/scholarship',
                'deadline' => now()->addMonths(3)->format('Y-m-d'),
                'eligibility_criteria' => [
                    'STEM major',
                    'Minimum GPA of 3.0',
                    'Portfolio of projects or research',
                    'Essay on innovation'
                ],
                'required_documents' => [
                    'Academic transcripts',
                    'Project portfolio',
                    'Innovation essay (750 words)',
                    'Faculty recommendation'
                ],
                'status' => 'active',
                'created_by' => $orgOfficer?->id ?? 1,
            ],
            [
                'title' => 'Community Service Leadership Award',
                'description' => 'Recognizing students who have made significant contributions to their communities through volunteer work and leadership. This scholarship celebrates civic engagement and social responsibility.',
                'amount' => 2500.00,
                'provider' => 'Community Foundation',
                'requirements' => 'Minimum 100 hours of community service, demonstrated leadership in volunteer activities, and commitment to continued community involvement.',
                'application_process' => 'Submit application with detailed community service log, leadership examples, and references from community organizations. Include reflection essay on impact of service.',
                'application_url' => 'https://communityfoundation.org/leadership-award',
                'deadline' => now()->addMonth()->format('Y-m-d'),
                'eligibility_criteria' => [
                    'Minimum 100 hours community service',
                    'Leadership experience',
                    'GPA of 2.5 or higher',
                    'Commitment to future service'
                ],
                'required_documents' => [
                    'Community service log',
                    'Leadership portfolio',
                    'Three community references',
                    'Reflection essay (600 words)'
                ],
                'status' => 'active',
                'created_by' => $admin?->id ?? 1,
            ],
            [
                'title' => 'First-Generation College Student Scholarship',
                'description' => 'Supporting students who are the first in their families to attend college. This scholarship provides both financial assistance and mentorship to help navigate the college experience successfully.',
                'amount' => 4000.00,
                'provider' => 'Educational Equity Foundation',
                'requirements' => 'Must be first-generation college student, demonstrate financial need, and maintain satisfactory academic progress. Participation in mentorship program required.',
                'application_process' => 'Complete comprehensive application including family education background, financial information, and personal statement about college goals and challenges.',
                'deadline' => now()->addMonths(4)->format('Y-m-d'),
                'eligibility_criteria' => [
                    'First-generation college student',
                    'Financial need demonstrated',
                    'Minimum GPA of 2.8',
                    'Willingness to participate in mentorship'
                ],
                'required_documents' => [
                    'Family education verification',
                    'Financial aid documents',
                    'Personal statement',
                    'Academic transcripts'
                ],
                'status' => 'active',
                'created_by' => $orgOfficer?->id ?? 1,
            ],
            [
                'title' => 'Arts and Creative Expression Grant',
                'description' => 'Fostering creativity and artistic expression among students pursuing arts, music, theater, and creative writing. This grant supports both traditional and innovative forms of artistic expression.',
                'amount' => 1500.00,
                'provider' => 'Arts Council',
                'requirements' => 'Must be pursuing arts-related field, submit portfolio of creative work, and demonstrate commitment to artistic development and community engagement through arts.',
                'application_process' => 'Submit digital portfolio showcasing best creative work, artist statement, and plan for using grant funds. Portfolio review by panel of arts professionals.',
                'application_url' => 'https://artscouncil.org/student-grants',
                'deadline' => now()->addWeeks(6)->format('Y-m-d'),
                'eligibility_criteria' => [
                    'Arts-related major or minor',
                    'Portfolio submission',
                    'Artist statement required',
                    'Community arts involvement'
                ],
                'required_documents' => [
                    'Digital portfolio (10-15 pieces)',
                    'Artist statement (400 words)',
                    'Academic transcripts',
                    'Arts faculty recommendation'
                ],
                'status' => 'active',
                'created_by' => $admin?->id ?? 1,
            ],
        ];

        foreach ($scholarships as $scholarshipData) {
            Scholarship::firstOrCreate(
                ['title' => $scholarshipData['title']],
                $scholarshipData
            );
        }
    }
}
