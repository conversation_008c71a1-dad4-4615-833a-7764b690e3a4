<?php

namespace Database\Seeders;

use App\Models\Organization;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class OrganizationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some sample users if they don't exist
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
                'role' => 'admin',
                'student_id' => 'ADM001',
                'email_verified_at' => now(),
            ]
        );

        $orgOfficer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Organization Officer',
                'password' => bcrypt('password'),
                'role' => 'org_officer',
                'student_id' => 'OFF001',
                'email_verified_at' => now(),
            ]
        );

        $student1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON>',
                'password' => bcrypt('password'),
                'role' => 'student',
                'student_id' => 'STU001',
                'email_verified_at' => now(),
            ]
        );

        $student2 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Jane Smith',
                'password' => bcrypt('password'),
                'role' => 'student',
                'student_id' => 'STU002',
                'email_verified_at' => now(),
            ]
        );

        // Create sample organizations
        $organizations = [
            [
                'name' => 'Student Council',
                'description' => 'The official student government representing all students on campus. We organize events, advocate for student rights, and serve as a bridge between students and administration.',
                'email' => '<EMAIL>',
                'phone' => '******-0101',
                'website' => 'https://studentcouncil.unilink.edu',
                'created_by' => $admin->id,
                'status' => 'active',
            ],
            [
                'name' => 'Computer Science Society',
                'description' => 'A community for computer science students and enthusiasts. We host coding competitions, tech talks, workshops, and networking events with industry professionals.',
                'email' => '<EMAIL>',
                'phone' => '******-0102',
                'website' => 'https://css.unilink.edu',
                'created_by' => $orgOfficer->id,
                'status' => 'active',
            ],
            [
                'name' => 'Drama Club',
                'description' => 'Express your creativity through theater! We produce plays, musicals, and dramatic performances throughout the academic year. All skill levels welcome.',
                'email' => '<EMAIL>',
                'phone' => '******-0103',
                'created_by' => $student1->id,
                'status' => 'active',
            ],
            [
                'name' => 'Environmental Action Group',
                'description' => 'Dedicated to promoting sustainability and environmental awareness on campus. Join us for clean-up drives, awareness campaigns, and green initiatives.',
                'email' => '<EMAIL>',
                'created_by' => $student2->id,
                'status' => 'active',
            ],
            [
                'name' => 'Photography Club',
                'description' => 'Capture the world through your lens! We organize photo walks, workshops, exhibitions, and competitions for photography enthusiasts of all levels.',
                'email' => '<EMAIL>',
                'created_by' => $student1->id,
                'status' => 'pending',
            ],
        ];

        foreach ($organizations as $orgData) {
            $orgData['slug'] = Str::slug($orgData['name']);

            $organization = Organization::firstOrCreate(
                ['slug' => $orgData['slug']],
                $orgData
            );

            // Add creator as president if not already a member
            if (!$organization->members()->where('user_id', $orgData['created_by'])->exists()) {
                $organization->members()->attach($orgData['created_by'], [
                    'role' => 'president',
                    'status' => 'active',
                    'joined_at' => now(),
                ]);
            }

            // Add some random members if organization doesn't have many members yet
            if ($organization->members()->count() < 5) {
                $members = User::where('id', '!=', $orgData['created_by'])
                    ->whereNotIn('id', $organization->members()->pluck('user_id'))
                    ->inRandomOrder()
                    ->limit(rand(3, 6))
                    ->get();

                foreach ($members as $member) {
                    $roles = ['member', 'member', 'member', 'officer']; // More members than officers
                    $role = $roles[array_rand($roles)];

                    $organization->members()->attach($member->id, [
                        'role' => $role,
                        'status' => 'active',
                        'joined_at' => now()->subDays(rand(1, 365)),
                    ]);
                }
            }
        }
    }
}
